import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../theme/app_theme.dart';
import '../providers/theme_provider.dart';

import '../models/chat_model.dart';
import '../services/chat_service.dart';
import 'chat_room_screen.dart';

class ChatScreen extends StatefulWidget {
  const ChatScreen({super.key});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  // خريطة لحفظ حالة العضوية لكل غرفة
  final Map<String, bool> _membershipStatus = {};

  @override
  void initState() {
    super.initState();
    _initializeChatService();
    _loadMembershipStatus();

    // إضافة مستمع لتغييرات التطبيق
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _debugPrintMembershipStatus();
    });
  }

  /// طباعة حالة العضوية للتشخيص
  void _debugPrintMembershipStatus() {
    if (kDebugMode) {
      print('حالة العضوية الحالية: $_membershipStatus');
    }
  }

  void _initializeChatService() {
    // تهيئة خدمة المحادثة وغرف المحادثة الافتراضية
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ChatService.initializeDefaultChatRooms();
    });
  }

  /// تحميل حالة العضوية من SharedPreferences و Firebase
  Future<void> _loadMembershipStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // قائمة معرفات الغرف المتوقعة (بما في ذلك العامة)
      final roomIds = ['general', 'year_1', 'year_2', 'year_3', 'year_4'];

      for (String roomId in roomIds) {
        // أولاً: تحميل من SharedPreferences (أسرع)
        final savedStatus = prefs.getBool('membership_$roomId');

        if (savedStatus != null) {
          if (mounted) {
            setState(() {
              _membershipStatus[roomId] = savedStatus;
            });
          }
        }

        // ثانياً: التحقق من Firebase للتأكد (في الخلفية)
        if (roomId == 'general') {
          // للغرفة العامة، إذا لم تكن محفوظة، اجعلها منضمة افتراضياً
          if (savedStatus == null) {
            if (mounted) {
              setState(() {
                _membershipStatus[roomId] = true;
              });
            }
            _saveMembershipStatus(roomId, true);
          }
        } else {
          // للغرف الأخرى، التحقق من Firebase (فقط إذا لم تكن محفوظة محلياً)
          if (savedStatus == null) {
            ChatService.isUserMember(roomId)
                .then((firebaseStatus) {
                  if (mounted) {
                    setState(() {
                      _membershipStatus[roomId] = firebaseStatus;
                    });
                  }
                  _saveMembershipStatus(roomId, firebaseStatus);
                })
                .catchError((error) {
                  // في حالة خطأ Firebase، افتراض عدم الانضمام
                  if (kDebugMode) {
                    print('خطأ في التحقق من Firebase للغرفة $roomId: $error');
                  }
                  if (mounted) {
                    setState(() {
                      _membershipStatus[roomId] = false;
                    });
                  }
                  _saveMembershipStatus(roomId, false);
                });
          }
          // إذا كانت محفوظة محلياً، لا نتحقق من Firebase لتجنب الكتابة فوق الحالة المحفوظة
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في تحميل حالة العضوية: $e');
      }
    }
  }

  /// حفظ حالة العضوية في SharedPreferences
  Future<void> _saveMembershipStatus(String roomId, bool isMember) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('membership_$roomId', isMember);

      if (kDebugMode) {
        print('تم حفظ حالة العضوية: $roomId = $isMember');
      }
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في حفظ حالة العضوية: $e');
      }
    }
  }

  // تم حذف الدوال غير المستخدمة

  // تم حذف _leaveChatRoom أيضاً

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          backgroundColor:
              themeProvider.isDarkMode
                  ? const Color(0xFF0F172A)
                  : AppTheme.backgroundColor,
          body: SafeArea(
            child: Column(
              children: [
                // Header
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [AppTheme.primaryColor, AppTheme.secondaryColor],
                    ),
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Icon(
                              Icons.chat_rounded,
                              color: Colors.white,
                              size: 24,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'الدردشة',
                                  style: GoogleFonts.cairo(
                                    fontSize: 24,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                                Text(
                                  'تواصل مع زملائك والأساتذة',
                                  style: GoogleFonts.cairo(
                                    fontSize: 14,
                                    color: Colors.white.withValues(alpha: 0.8),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Container(
                                  width: 8,
                                  height: 8,
                                  decoration: const BoxDecoration(
                                    color: Colors.green,
                                    shape: BoxShape.circle,
                                  ),
                                ),
                                const SizedBox(width: 6),
                                Text(
                                  'متصل',
                                  style: GoogleFonts.cairo(
                                    fontSize: 12,
                                    color: Colors.white,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                // Content
                Expanded(child: _buildChatContent()),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildChatContent() {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return StreamBuilder<List<ChatRoomModel>>(
          stream: ChatService.getChatRoomsStream(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }

            if (snapshot.hasError) {
              return Center(
                child: Text(
                  'خطأ في تحميل غرف المحادثة',
                  style: GoogleFonts.cairo(
                    color:
                        themeProvider.isDarkMode
                            ? Colors.white
                            : Colors.black87,
                  ),
                ),
              );
            }

            final chatRooms = snapshot.data ?? [];
            return _buildChatRoomsList(chatRooms, themeProvider);
          },
        );
      },
    );
  }

  Widget _buildChatRoomsList(
    List<ChatRoomModel> chatRooms,
    ThemeProvider themeProvider,
  ) {
    if (chatRooms.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.chat_bubble_outline, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'لا توجد غرف محادثة متاحة',
              style: GoogleFonts.cairo(fontSize: 16, color: Colors.grey[600]),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان المحادثة العامة
          if (chatRooms.any((room) => room.isGeneral)) ...[
            Text(
              'المحادثة العامة',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: themeProvider.isDarkMode ? Colors.white : Colors.black87,
              ),
            ),
            const SizedBox(height: 12),

            // عرض الغرفة العامة
            ...chatRooms
                .where((room) => room.isGeneral)
                .map(
                  (room) => Padding(
                    padding: const EdgeInsets.only(bottom: 12),
                    child: _buildChatCard(room, themeProvider),
                  ),
                ),
            const SizedBox(height: 24),
          ],

          // عنوان محادثات السنوات الدراسية
          if (chatRooms.any((room) => !room.isGeneral)) ...[
            Text(
              'محادثات السنوات الدراسية',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: themeProvider.isDarkMode ? Colors.white : Colors.black87,
              ),
            ),
            const SizedBox(height: 16),

            // عرض غرف السنوات الدراسية
            ...chatRooms
                .where((room) => !room.isGeneral)
                .map(
                  (room) => Padding(
                    padding: const EdgeInsets.only(bottom: 12),
                    child: _buildChatCard(room, themeProvider),
                  ),
                ),
          ],
        ],
      ),
    );
  }

  // تم حذف _buildHybridChatContent - غير مستخدمة

  /// التحقق من عضوية المستخدم في الغرفة
  bool _isUserMemberOfRoom(ChatRoomModel chatRoom) {
    if (kDebugMode) {
      print(
        'التحقق من العضوية للغرفة ${chatRoom.id}: ${_membershipStatus[chatRoom.id]}',
      );
    }

    // للغرفة العامة، التحقق من الحالة المحفوظة أو افتراض الانضمام
    if (chatRoom.isGeneral) {
      return _membershipStatus[chatRoom.id] ?? true; // افتراضياً منضم للعامة
    }

    // للغرف الأخرى، التحقق من العضوية المحفوظة محلياً
    return _membershipStatus[chatRoom.id] ?? false;
  }

  /// التعامل مع الانضمام للغرفة
  Future<void> _handleJoinRoom(ChatRoomModel chatRoom) async {
    try {
      // إظهار مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );

      // محاولة الانضمام للغرفة
      final success = await ChatService.joinChatRoom(chatRoom.id);

      // إغلاق مؤشر التحميل
      if (mounted) Navigator.of(context).pop();

      if (success) {
        // تحديث حالة العضوية محلياً
        if (mounted) {
          setState(() {
            _membershipStatus[chatRoom.id] = true;
          });
        }

        // حفظ الحالة بشكل دائم مع تأكيد إضافي
        await _saveMembershipStatus(chatRoom.id, true);

        // تأكيد إضافي للحفظ
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool('membership_${chatRoom.id}', true);

        if (kDebugMode) {
          print('تأكيد الانضمام: ${chatRoom.id} = true');
        }

        // إظهار رسالة نجاح
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'تم الانضمام إلى ${chatRoom.name} بنجاح',
                style: GoogleFonts.cairo(),
              ),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        // إظهار رسالة خطأ
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'فشل في الانضمام إلى ${chatRoom.name}',
                style: GoogleFonts.cairo(),
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      // إغلاق مؤشر التحميل في حالة الخطأ
      if (mounted) Navigator.of(context).pop();

      // إظهار رسالة خطأ
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء الانضمام', style: GoogleFonts.cairo()),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// التعامل مع مغادرة الغرفة
  Future<void> _handleLeaveRoom(ChatRoomModel chatRoom) async {
    try {
      // تأكيد المغادرة
      final shouldLeave = await showDialog<bool>(
        context: context,
        builder:
            (context) => AlertDialog(
              title: Text(
                'مغادرة ${chatRoom.name}',
                style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
              ),
              content: Text(
                'هل أنت متأكد من رغبتك في مغادرة هذه المجموعة؟',
                style: GoogleFonts.cairo(),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: Text('إلغاء', style: GoogleFonts.cairo()),
                ),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                  child: Text(
                    'مغادرة',
                    style: GoogleFonts.cairo(color: Colors.white),
                  ),
                ),
              ],
            ),
      );

      if (shouldLeave != true) return;

      // إظهار مؤشر التحميل
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder:
              (context) => const Center(child: CircularProgressIndicator()),
        );
      }

      // محاولة مغادرة الغرفة
      final success = await ChatService.leaveChatRoom(chatRoom.id);

      // إغلاق مؤشر التحميل
      if (mounted) Navigator.of(context).pop();

      if (success) {
        // تحديث حالة العضوية محلياً
        if (mounted) {
          setState(() {
            _membershipStatus[chatRoom.id] = false;
          });
        }

        // حفظ الحالة بشكل دائم
        await _saveMembershipStatus(chatRoom.id, false);

        // إظهار رسالة نجاح
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'تم مغادرة ${chatRoom.name} بنجاح',
                style: GoogleFonts.cairo(),
              ),
              backgroundColor: Colors.orange,
            ),
          );
        }
      } else {
        // إظهار رسالة خطأ
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'فشل في مغادرة ${chatRoom.name}',
                style: GoogleFonts.cairo(),
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      // إغلاق مؤشر التحميل في حالة الخطأ
      if (mounted) Navigator.of(context).pop();

      // إظهار رسالة خطأ
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء المغادرة', style: GoogleFonts.cairo()),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildChatCard(ChatRoomModel chatRoom, ThemeProvider themeProvider) {
    // التحقق من عضوية المستخدم
    final isUserMember = _isUserMemberOfRoom(chatRoom);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors:
              isUserMember
                  ? (themeProvider.isDarkMode
                      ? [const Color(0xFF1E293B), const Color(0xFF334155)]
                      : [Colors.white, Colors.grey[50]!])
                  : (themeProvider.isDarkMode
                      ? [const Color(0xFF1F2937), const Color(0xFF374151)]
                      : [Colors.grey[100]!, Colors.grey[200]!]),
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color:
              isUserMember
                  ? AppTheme.primaryColor.withValues(alpha: 0.3)
                  : Colors.grey.withValues(alpha: 0.3),
          width: isUserMember ? 1.5 : 1,
        ),
        boxShadow:
            isUserMember
                ? [
                  BoxShadow(
                    color: AppTheme.primaryColor.withValues(alpha: 0.15),
                    blurRadius: 15,
                    offset: const Offset(0, 5),
                    spreadRadius: 0,
                  ),
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ]
                : [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
      ),
      child: InkWell(
        onTap: () async {
          // الانتقال إلى شاشة المحادثة
          await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ChatRoomScreen(chatRoom: chatRoom),
            ),
          );

          // تحديث حالة العضوية عند العودة (فقط إذا لم تكن محفوظة)
          if (!_membershipStatus.containsKey(chatRoom.id) ||
              _membershipStatus[chatRoom.id] == null) {
            final isMember =
                chatRoom.isGeneral
                    ? true // للغرفة العامة، افتراضياً منضم
                    : await ChatService.isUserMember(chatRoom.id);

            if (mounted) {
              setState(() {
                _membershipStatus[chatRoom.id] = isMember;
              });
            }

            // حفظ الحالة المحدثة
            await _saveMembershipStatus(chatRoom.id, isMember);
          }
        },
        borderRadius: BorderRadius.circular(20),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors:
                        isUserMember
                            ? (chatRoom.isGeneral
                                ? [
                                  const Color(0xFF667EEA),
                                  const Color(0xFF764BA2),
                                ]
                                : [
                                  const Color(0xFF4FACFE),
                                  const Color(0xFF00F2FE),
                                ])
                            : [Colors.grey[400]!, Colors.grey[500]!],
                  ),
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color:
                          isUserMember
                              ? (chatRoom.isGeneral
                                      ? const Color(0xFF667EEA)
                                      : const Color(0xFF4FACFE))
                                  .withValues(alpha: 0.4)
                              : Colors.grey.withValues(alpha: 0.2),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Icon(
                  isUserMember
                      ? (chatRoom.isGeneral
                          ? Icons.public_rounded
                          : Icons.school_rounded)
                      : (chatRoom.isGeneral
                          ? Icons.public_outlined
                          : Icons.lock_outline_rounded),
                  color: isUserMember ? Colors.white : Colors.grey[300],
                  size: 28,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Row(
                            children: [
                              Flexible(
                                child: Text(
                                  chatRoom.name,
                                  style: GoogleFonts.cairo(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w700,
                                    color:
                                        themeProvider.isDarkMode
                                            ? const Color(0xFFF1F5F9)
                                            : AppTheme.textColor,
                                  ),
                                ),
                              ),
                              if (chatRoom.isGeneral) ...[
                                const SizedBox(width: 8),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 6,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    gradient: const LinearGradient(
                                      colors: [
                                        Color(0xFFFFD700),
                                        Color(0xFFFFA500),
                                      ],
                                    ),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Text(
                                    'عامة',
                                    style: GoogleFonts.cairo(
                                      fontSize: 9,
                                      color: Colors.white,
                                      fontWeight: FontWeight.w700,
                                    ),
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ),
                        // أيقونة الدردشة
                        Icon(
                          Icons.chat_bubble_outline,
                          size: 16,
                          color: AppTheme.primaryColor,
                        ),
                      ],
                    ),
                    const SizedBox(height: 6),
                    Text(
                      chatRoom.description,
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color: Colors.grey[600],
                        height: 1.3,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          Icons.people_outline_rounded,
                          size: 14,
                          color: Colors.grey[500],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          isUserMember ? 'منضم' : 'غير منضم',
                          style: GoogleFonts.cairo(
                            fontSize: 11,
                            color:
                                isUserMember
                                    ? Colors.green[600]
                                    : Colors.grey[500],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const Spacer(),
                        GestureDetector(
                          onTap:
                              isUserMember
                                  ? null
                                  : () => _handleJoinRoom(chatRoom),
                          onLongPress:
                              isUserMember
                                  ? () => _handleLeaveRoom(chatRoom)
                                  : null,
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors:
                                    isUserMember
                                        ? [Colors.green, Colors.green[700]!]
                                        : [
                                          AppTheme.primaryColor,
                                          AppTheme.secondaryColor,
                                        ],
                              ),
                              borderRadius: BorderRadius.circular(25),
                              boxShadow: [
                                BoxShadow(
                                  color: (isUserMember
                                          ? Colors.green
                                          : AppTheme.primaryColor)
                                      .withValues(alpha: 0.3),
                                  blurRadius: 8,
                                  offset: const Offset(0, 4),
                                ),
                              ],
                            ),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 8,
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    isUserMember
                                        ? Icons.check_circle
                                        : Icons.login_rounded,
                                    size: 16,
                                    color: Colors.white,
                                  ),
                                  const SizedBox(width: 6),
                                  Text(
                                    isUserMember ? 'منضم' : 'انضمام',
                                    style: GoogleFonts.cairo(
                                      fontSize: 13,
                                      fontWeight: FontWeight.w700,
                                      color: Colors.white,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // تم حذف جميع الدوال غير المستخدمة
}

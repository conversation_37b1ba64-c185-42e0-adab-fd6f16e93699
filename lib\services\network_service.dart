import 'dart:async';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import '../utils/logger.dart';
import 'smart_cache_service.dart';

/// خدمة إدارة الشبكة مع Retry Logic وOffline Handling
class NetworkService {
  static final Connectivity _connectivity = Connectivity();
  static StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  static bool _isOnline = true;
  static final List<Function()> _retryQueue = [];
  static Timer? _retryTimer;
  
  // إعدادات Retry
  static const int _maxRetries = 3;
  static const Duration _retryDelay = Duration(seconds: 2);
  static const Duration _timeoutDuration = Duration(seconds: 10);
  
  /// تهيئة خدمة الشبكة
  static Future<void> initialize() async {
    try {
      // فحص الاتصال الأولي
      await _checkInitialConnectivity();
      
      // الاستماع لتغييرات الاتصال
      _startConnectivityListener();
      
      AppLogger.info('تم تهيئة خدمة الشبكة بنجاح', 'NetworkService');
    } catch (e) {
      AppLogger.error('خطأ في تهيئة خدمة الشبكة', 'NetworkService', e);
    }
  }
  
  /// فحص الاتصال الأولي
  static Future<void> _checkInitialConnectivity() async {
    try {
      final connectivityResults = await _connectivity.checkConnectivity();
      _updateConnectionStatus(connectivityResults);
    } catch (e) {
      AppLogger.error('خطأ في فحص الاتصال الأولي', 'NetworkService', e);
      _isOnline = false;
    }
  }
  
  /// بدء الاستماع لتغييرات الاتصال
  static void _startConnectivityListener() {
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
      _updateConnectionStatus,
      onError: (error) {
        AppLogger.error('خطأ في مراقبة الاتصال', 'NetworkService', error);
      },
    );
  }
  
  /// تحديث حالة الاتصال
  static void _updateConnectionStatus(List<ConnectivityResult> results) {
    final wasOnline = _isOnline;
    _isOnline = results.any((result) => 
        result != ConnectivityResult.none);
    
    if (!wasOnline && _isOnline) {
      AppLogger.info('تم استعادة الاتصال بالإنترنت', 'NetworkService');
      _processRetryQueue();
    } else if (wasOnline && !_isOnline) {
      AppLogger.warning('انقطع الاتصال بالإنترنت', 'NetworkService');
    }
  }
  
  /// التحقق من حالة الاتصال
  static bool get isOnline => _isOnline;
  
  /// تنفيذ طلب مع Retry Logic
  static Future<T> executeWithRetry<T>(
    Future<T> Function() operation, {
    int maxRetries = _maxRetries,
    Duration retryDelay = _retryDelay,
    Duration timeout = _timeoutDuration,
    bool useCache = true,
    String? cacheKey,
  }) async {
    int attempts = 0;
    
    while (attempts <= maxRetries) {
      try {
        // محاولة الحصول على البيانات من Cache إذا كان متاحاً
        if (useCache && cacheKey != null && !_isOnline) {
          final cachedData = await SmartCacheService.get<T>(cacheKey);
          if (cachedData != null) {
            AppLogger.info('تم استرجاع البيانات من Cache (offline)', 'NetworkService');
            return cachedData;
          }
        }
        
        // تنفيذ العملية مع timeout
        final result = await operation().timeout(timeout);
        
        // حفظ النتيجة في Cache إذا نجحت العملية
        if (useCache && cacheKey != null && result != null) {
          await SmartCacheService.set(
            cacheKey,
            result,
            ttlMinutes: 30,
            persistToDisk: true,
          );
        }
        
        return result;
      } catch (e) {
        attempts++;
        
        if (attempts > maxRetries) {
          // إذا فشلت جميع المحاولات، حاول الحصول على البيانات من Cache
          if (useCache && cacheKey != null) {
            final cachedData = await SmartCacheService.get<T>(cacheKey);
            if (cachedData != null) {
              AppLogger.warning(
                'فشل في تحميل البيانات، تم استرجاعها من Cache',
                'NetworkService',
              );
              return cachedData;
            }
          }
          
          AppLogger.error(
            'فشل في تنفيذ العملية بعد $maxRetries محاولات',
            'NetworkService',
            e,
          );
          rethrow;
        }
        
        AppLogger.warning(
          'فشلت المحاولة $attempts، إعادة المحاولة خلال ${retryDelay.inSeconds} ثانية',
          'NetworkService',
        );
        
        await Future.delayed(retryDelay);
      }
    }
    
    throw Exception('فشل في تنفيذ العملية');
  }
  
  /// إضافة عملية لقائمة الانتظار للتنفيذ عند عودة الاتصال
  static void addToRetryQueue(Function() operation) {
    _retryQueue.add(operation);
    AppLogger.info('تم إضافة عملية لقائمة الانتظار', 'NetworkService');
  }
  
  /// معالجة قائمة الانتظار عند عودة الاتصال
  static void _processRetryQueue() {
    if (_retryQueue.isEmpty) return;
    
    AppLogger.info('معالجة ${_retryQueue.length} عملية من قائمة الانتظار', 'NetworkService');
    
    // تأخير قصير للتأكد من استقرار الاتصال
    _retryTimer?.cancel();
    _retryTimer = Timer(const Duration(seconds: 1), () {
      final operations = List<Function()>.from(_retryQueue);
      _retryQueue.clear();
      
      for (final operation in operations) {
        try {
          operation();
        } catch (e) {
          AppLogger.error('خطأ في تنفيذ عملية من قائمة الانتظار', 'NetworkService', e);
        }
      }
    });
  }
  
  /// فحص الاتصال بالإنترنت عبر ping
  static Future<bool> hasInternetConnection() async {
    try {
      final result = await InternetAddress.lookup('google.com')
          .timeout(const Duration(seconds: 5));
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      return false;
    }
  }
  
  /// الحصول على نوع الاتصال
  static Future<String> getConnectionType() async {
    try {
      final connectivityResults = await _connectivity.checkConnectivity();
      if (connectivityResults.contains(ConnectivityResult.wifi)) {
        return 'WiFi';
      } else if (connectivityResults.contains(ConnectivityResult.mobile)) {
        return 'Mobile Data';
      } else if (connectivityResults.contains(ConnectivityResult.ethernet)) {
        return 'Ethernet';
      } else {
        return 'No Connection';
      }
    } catch (e) {
      return 'Unknown';
    }
  }
  
  /// الحصول على إحصائيات الشبكة
  static Map<String, dynamic> getNetworkStats() {
    return {
      'isOnline': _isOnline,
      'retryQueueSize': _retryQueue.length,
      'isRetryTimerActive': _retryTimer?.isActive ?? false,
    };
  }
  
  /// تنظيف الموارد
  static void dispose() {
    _connectivitySubscription?.cancel();
    _retryTimer?.cancel();
    _retryQueue.clear();
  }
}

/// خدمة مزامنة البيانات للعمل Offline
class OfflineSyncService {
  static final List<OfflineOperation> _pendingOperations = [];
  static Timer? _syncTimer;
  
  /// بدء خدمة المزامنة
  static void startSync() {
    _syncTimer?.cancel();
    _syncTimer = Timer.periodic(
      const Duration(minutes: 1),
      (_) => _syncPendingOperations(),
    );
  }
  
  /// إضافة عملية للمزامنة
  static void addOperation(OfflineOperation operation) {
    _pendingOperations.add(operation);
    AppLogger.info('تم إضافة عملية للمزامنة: ${operation.type}', 'OfflineSyncService');
    
    // محاولة المزامنة فوراً إذا كان هناك اتصال
    if (NetworkService.isOnline) {
      _syncPendingOperations();
    }
  }
  
  /// مزامنة العمليات المعلقة
  static Future<void> _syncPendingOperations() async {
    if (_pendingOperations.isEmpty || !NetworkService.isOnline) return;
    
    final operationsToSync = List<OfflineOperation>.from(_pendingOperations);
    _pendingOperations.clear();
    
    for (final operation in operationsToSync) {
      try {
        await operation.execute();
        AppLogger.info('تم تنفيذ عملية مزامنة: ${operation.type}', 'OfflineSyncService');
      } catch (e) {
        // إعادة إضافة العملية للمحاولة لاحقاً
        _pendingOperations.add(operation);
        AppLogger.error('فشل في مزامنة عملية: ${operation.type}', 'OfflineSyncService', e);
      }
    }
  }
  
  /// الحصول على عدد العمليات المعلقة
  static int get pendingOperationsCount => _pendingOperations.length;
  
  /// تنظيف الموارد
  static void dispose() {
    _syncTimer?.cancel();
    _pendingOperations.clear();
  }
}

/// عملية offline للمزامنة لاحقاً
class OfflineOperation {
  final String type;
  final Map<String, dynamic> data;
  final Future<void> Function() execute;
  final DateTime createdAt;
  
  OfflineOperation({
    required this.type,
    required this.data,
    required this.execute,
  }) : createdAt = DateTime.now();
}

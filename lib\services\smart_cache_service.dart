import 'dart:async';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import '../models/community_post.dart';
import '../models/chat_model.dart';
import '../models/pdf_model.dart';
import '../utils/logger.dart';

/// خدمة Cache ذكية مع انتهاء صلاحية وإدارة ذاكرة محسنة
class SmartCacheService {
  static final Map<String, CacheItem> _memoryCache = {};
  static SharedPreferences? _prefs;
  static Timer? _cleanupTimer;
  
  // إعدادات Cache
  static const int _maxMemoryCacheSize = 100; // عدد العناصر في الذاكرة
  static const int _defaultTtlMinutes = 30; // مدة انتهاء الصلاحية الافتراضية
  static const int _maxDiskCacheSize = 50; // عدد العناصر في التخزين المحلي
  
  /// تهيئة الخدمة
  static Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      _startCleanupTimer();
      await _cleanExpiredItems();
      
      AppLogger.info('تم تهيئة Smart Cache بنجاح', 'SmartCacheService');
    } catch (e) {
      AppLogger.error('خطأ في تهيئة Smart Cache', 'SmartCacheService', e);
    }
  }
  
  /// بدء مؤقت التنظيف التلقائي
  static void _startCleanupTimer() {
    _cleanupTimer?.cancel();
    _cleanupTimer = Timer.periodic(
      const Duration(minutes: 10),
      (_) => _cleanExpiredItems(),
    );
  }
  
  /// حفظ البيانات في Cache
  static Future<void> set<T>(
    String key,
    T data, {
    int? ttlMinutes,
    bool persistToDisk = false,
  }) async {
    try {
      final ttl = ttlMinutes ?? _defaultTtlMinutes;
      final expiryTime = DateTime.now().add(Duration(minutes: ttl));
      
      final cacheItem = CacheItem(
        data: data,
        expiryTime: expiryTime,
        accessCount: 1,
        lastAccessed: DateTime.now(),
      );
      
      // حفظ في الذاكرة
      _memoryCache[key] = cacheItem;
      
      // تنظيف الذاكرة إذا تجاوزت الحد الأقصى
      if (_memoryCache.length > _maxMemoryCacheSize) {
        _cleanOldestMemoryItems();
      }
      
      // حفظ في التخزين المحلي إذا طُلب ذلك
      if (persistToDisk && _prefs != null) {
        await _saveToDisk(key, cacheItem);
      }
      
      AppLogger.debug('تم حفظ $key في Cache', 'SmartCacheService');
    } catch (e) {
      AppLogger.error('خطأ في حفظ $key في Cache', 'SmartCacheService', e);
    }
  }
  
  /// استرجاع البيانات من Cache
  static Future<T?> get<T>(String key) async {
    try {
      // البحث في الذاكرة أولاً
      if (_memoryCache.containsKey(key)) {
        final item = _memoryCache[key]!;
        
        // التحقق من انتهاء الصلاحية
        if (item.isExpired) {
          _memoryCache.remove(key);
          await _removeFromDisk(key);
          return null;
        }
        
        // تحديث إحصائيات الوصول
        item.accessCount++;
        item.lastAccessed = DateTime.now();
        
        AppLogger.debug('تم استرجاع $key من Memory Cache', 'SmartCacheService');
        return item.data as T?;
      }
      
      // البحث في التخزين المحلي
      if (_prefs != null) {
        final diskItem = await _loadFromDisk<T>(key);
        if (diskItem != null && !diskItem.isExpired) {
          // نقل إلى الذاكرة للوصول السريع
          _memoryCache[key] = diskItem;
          
          AppLogger.debug('تم استرجاع $key من Disk Cache', 'SmartCacheService');
          return diskItem.data as T?;
        } else if (diskItem != null) {
          // حذف العنصر المنتهي الصلاحية
          await _removeFromDisk(key);
        }
      }
      
      return null;
    } catch (e) {
      AppLogger.error('خطأ في استرجاع $key من Cache', 'SmartCacheService', e);
      return null;
    }
  }
  
  /// حذف عنصر من Cache
  static Future<void> remove(String key) async {
    try {
      _memoryCache.remove(key);
      await _removeFromDisk(key);
      
      AppLogger.debug('تم حذف $key من Cache', 'SmartCacheService');
    } catch (e) {
      AppLogger.error('خطأ في حذف $key من Cache', 'SmartCacheService', e);
    }
  }
  
  /// مسح Cache بالكامل
  static Future<void> clear() async {
    try {
      _memoryCache.clear();
      
      if (_prefs != null) {
        final keys = _prefs!.getKeys().where((k) => k.startsWith('cache_'));
        for (final key in keys) {
          await _prefs!.remove(key);
        }
      }
      
      AppLogger.info('تم مسح Cache بالكامل', 'SmartCacheService');
    } catch (e) {
      AppLogger.error('خطأ في مسح Cache', 'SmartCacheService', e);
    }
  }
  
  /// تنظيف العناصر المنتهية الصلاحية
  static Future<void> _cleanExpiredItems() async {
    try {
      // تنظيف الذاكرة
      final expiredKeys = _memoryCache.entries
          .where((entry) => entry.value.isExpired)
          .map((entry) => entry.key)
          .toList();
      
      for (final key in expiredKeys) {
        _memoryCache.remove(key);
      }
      
      // تنظيف التخزين المحلي
      if (_prefs != null) {
        final cacheKeys = _prefs!.getKeys().where((k) => k.startsWith('cache_'));
        for (final key in cacheKeys) {
          final item = await _loadFromDisk(key.substring(6)); // إزالة 'cache_'
          if (item?.isExpired == true) {
            await _prefs!.remove(key);
          }
        }
      }
      
      if (expiredKeys.isNotEmpty) {
        AppLogger.info('تم تنظيف ${expiredKeys.length} عنصر منتهي الصلاحية', 'SmartCacheService');
      }
    } catch (e) {
      AppLogger.error('خطأ في تنظيف Cache', 'SmartCacheService', e);
    }
  }
  
  /// تنظيف أقدم العناصر من الذاكرة
  static void _cleanOldestMemoryItems() {
    final sortedEntries = _memoryCache.entries.toList()
      ..sort((a, b) => a.value.lastAccessed.compareTo(b.value.lastAccessed));
    
    final itemsToRemove = sortedEntries.take(_memoryCache.length - _maxMemoryCacheSize + 10);
    for (final entry in itemsToRemove) {
      _memoryCache.remove(entry.key);
    }
  }
  
  /// حفظ في التخزين المحلي
  static Future<void> _saveToDisk(String key, CacheItem item) async {
    try {
      final json = {
        'data': _serializeData(item.data),
        'expiryTime': item.expiryTime.millisecondsSinceEpoch,
        'accessCount': item.accessCount,
        'lastAccessed': item.lastAccessed.millisecondsSinceEpoch,
      };
      
      await _prefs!.setString('cache_$key', jsonEncode(json));
    } catch (e) {
      AppLogger.error('خطأ في حفظ $key في Disk', 'SmartCacheService', e);
    }
  }
  
  /// تحميل من التخزين المحلي
  static Future<CacheItem?> _loadFromDisk<T>(String key) async {
    try {
      final jsonString = _prefs!.getString('cache_$key');
      if (jsonString == null) return null;
      
      final json = jsonDecode(jsonString);
      
      return CacheItem(
        data: _deserializeData<T>(json['data']),
        expiryTime: DateTime.fromMillisecondsSinceEpoch(json['expiryTime']),
        accessCount: json['accessCount'] ?? 1,
        lastAccessed: DateTime.fromMillisecondsSinceEpoch(json['lastAccessed']),
      );
    } catch (e) {
      AppLogger.error('خطأ في تحميل $key من Disk', 'SmartCacheService', e);
      return null;
    }
  }
  
  /// حذف من التخزين المحلي
  static Future<void> _removeFromDisk(String key) async {
    try {
      await _prefs?.remove('cache_$key');
    } catch (e) {
      AppLogger.error('خطأ في حذف $key من Disk', 'SmartCacheService', e);
    }
  }
  
  /// تسلسل البيانات للحفظ
  static dynamic _serializeData(dynamic data) {
    if (data is CommunityPost) {
      return {'type': 'CommunityPost', 'data': data.toJson()};
    } else if (data is MessageModel) {
      return {'type': 'MessageModel', 'data': data.toJson()};
    } else if (data is PDFModel) {
      return {'type': 'PDFModel', 'data': data.toJson()};
    } else if (data is List) {
      return {'type': 'List', 'data': data.map(_serializeData).toList()};
    }
    return {'type': 'primitive', 'data': data};
  }
  
  /// إلغاء تسلسل البيانات
  static T? _deserializeData<T>(dynamic serialized) {
    try {
      if (serialized is! Map) return serialized as T?;
      
      final type = serialized['type'];
      final data = serialized['data'];
      
      switch (type) {
        case 'CommunityPost':
          return CommunityPost.fromJson(data) as T?;
        case 'MessageModel':
          return MessageModel.fromJson(data) as T?;
        case 'PDFModel':
          return PDFModel.fromJson(data) as T?;
        case 'List':
          return (data as List).map((item) => _deserializeData(item)).toList() as T?;
        case 'primitive':
        default:
          return data as T?;
      }
    } catch (e) {
      AppLogger.error('خطأ في إلغاء تسلسل البيانات', 'SmartCacheService', e);
      return null;
    }
  }
  
  /// الحصول على إحصائيات Cache
  static Map<String, dynamic> getStats() {
    final memorySize = _memoryCache.length;
    final totalAccess = _memoryCache.values.fold<int>(0, (sum, item) => sum + item.accessCount);
    
    return {
      'memoryItems': memorySize,
      'maxMemorySize': _maxMemoryCacheSize,
      'totalAccess': totalAccess,
      'averageAccess': memorySize > 0 ? (totalAccess / memorySize).toStringAsFixed(1) : '0',
      'isCleanupActive': _cleanupTimer?.isActive ?? false,
    };
  }
  
  /// إيقاف الخدمة
  static void dispose() {
    _cleanupTimer?.cancel();
    _memoryCache.clear();
  }
}

/// عنصر Cache مع معلومات انتهاء الصلاحية والوصول
class CacheItem {
  dynamic data;
  DateTime expiryTime;
  int accessCount;
  DateTime lastAccessed;
  
  CacheItem({
    required this.data,
    required this.expiryTime,
    this.accessCount = 1,
    DateTime? lastAccessed,
  }) : lastAccessed = lastAccessed ?? DateTime.now();
  
  bool get isExpired => DateTime.now().isAfter(expiryTime);
}

import 'package:flutter/material.dart';

/// مساعد للتصميم المتجاوب
class ResponsiveHelper {
  
  /// أحجام الشاشات
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 900;
  static const double desktopBreakpoint = 1200;
  
  /// التحقق من نوع الجهاز
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobileBreakpoint;
  }
  
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobileBreakpoint && width < tabletBreakpoint;
  }
  
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= tabletBreakpoint;
  }
  
  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }
  
  static bool isPortrait(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.portrait;
  }
  
  /// الحصول على عرض الشاشة
  static double getScreenWidth(BuildContext context) {
    return MediaQuery.of(context).size.width;
  }
  
  static double getScreenHeight(BuildContext context) {
    return MediaQuery.of(context).size.height;
  }
  
  /// الحصول على نوع الجهاز كـ enum
  static DeviceType getDeviceType(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width < mobileBreakpoint) return DeviceType.mobile;
    if (width < tabletBreakpoint) return DeviceType.tablet;
    return DeviceType.desktop;
  }
  
  /// الحصول على عدد الأعمدة المناسب للشبكة
  static int getGridColumns(BuildContext context) {
    if (isMobile(context)) {
      return isLandscape(context) ? 2 : 1;
    } else if (isTablet(context)) {
      return isLandscape(context) ? 3 : 2;
    } else {
      return isLandscape(context) ? 4 : 3;
    }
  }
  
  /// الحصول على padding مناسب
  static EdgeInsets getScreenPadding(BuildContext context) {
    if (isMobile(context)) {
      return const EdgeInsets.all(16);
    } else if (isTablet(context)) {
      return const EdgeInsets.all(24);
    } else {
      return const EdgeInsets.all(32);
    }
  }
  
  /// الحصول على حجم خط مناسب
  static double getFontSize(BuildContext context, double baseFontSize) {
    if (isMobile(context)) {
      return baseFontSize;
    } else if (isTablet(context)) {
      return baseFontSize * 1.1;
    } else {
      return baseFontSize * 1.2;
    }
  }
  
  /// الحصول على حجم أيقونة مناسب
  static double getIconSize(BuildContext context, double baseIconSize) {
    if (isMobile(context)) {
      return baseIconSize;
    } else if (isTablet(context)) {
      return baseIconSize * 1.2;
    } else {
      return baseIconSize * 1.4;
    }
  }
  
  /// الحصول على عرض مناسب للمحتوى
  static double getContentWidth(BuildContext context) {
    final screenWidth = getScreenWidth(context);
    if (isMobile(context)) {
      return screenWidth;
    } else if (isTablet(context)) {
      return screenWidth * 0.8;
    } else {
      return 800; // حد أقصى للمحتوى على الشاشات الكبيرة
    }
  }
  
  /// الحصول على ارتفاع مناسب للعناصر
  static double getItemHeight(BuildContext context, double baseHeight) {
    if (isMobile(context)) {
      return baseHeight;
    } else if (isTablet(context)) {
      return baseHeight * 1.1;
    } else {
      return baseHeight * 1.2;
    }
  }
  
  /// الحصول على spacing مناسب
  static double getSpacing(BuildContext context, double baseSpacing) {
    if (isMobile(context)) {
      return baseSpacing;
    } else if (isTablet(context)) {
      return baseSpacing * 1.2;
    } else {
      return baseSpacing * 1.5;
    }
  }
}

/// أنواع الأجهزة
enum DeviceType {
  mobile,
  tablet,
  desktop,
}

/// ويدجت للتخطيط المتجاوب
class ResponsiveBuilder extends StatelessWidget {
  final Widget Function(BuildContext context, DeviceType deviceType) builder;
  
  const ResponsiveBuilder({
    super.key,
    required this.builder,
  });
  
  @override
  Widget build(BuildContext context) {
    final deviceType = ResponsiveHelper.getDeviceType(context);
    return builder(context, deviceType);
  }
}

/// ويدجت للتخطيط المتجاوب مع خيارات مختلفة
class ResponsiveWidget extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;
  
  const ResponsiveWidget({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
  });
  
  @override
  Widget build(BuildContext context) {
    if (ResponsiveHelper.isDesktop(context) && desktop != null) {
      return desktop!;
    } else if (ResponsiveHelper.isTablet(context) && tablet != null) {
      return tablet!;
    } else {
      return mobile;
    }
  }
}

/// ويدجت للتخطيط حسب الاتجاه
class OrientationBuilder extends StatelessWidget {
  final Widget portrait;
  final Widget? landscape;
  
  const OrientationBuilder({
    super.key,
    required this.portrait,
    this.landscape,
  });
  
  @override
  Widget build(BuildContext context) {
    if (ResponsiveHelper.isLandscape(context) && landscape != null) {
      return landscape!;
    } else {
      return portrait;
    }
  }
}

/// ويدجت للمحتوى المحدود العرض
class ConstrainedContent extends StatelessWidget {
  final Widget child;
  final double? maxWidth;
  
  const ConstrainedContent({
    super.key,
    required this.child,
    this.maxWidth,
  });
  
  @override
  Widget build(BuildContext context) {
    final contentWidth = maxWidth ?? ResponsiveHelper.getContentWidth(context);
    
    return Center(
      child: ConstrainedBox(
        constraints: BoxConstraints(maxWidth: contentWidth),
        child: child,
      ),
    );
  }
}

/// ويدجت للشبكة المتجاوبة
class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final double spacing;
  final double runSpacing;
  final int? forceColumns;
  
  const ResponsiveGrid({
    super.key,
    required this.children,
    this.spacing = 16,
    this.runSpacing = 16,
    this.forceColumns,
  });
  
  @override
  Widget build(BuildContext context) {
    final columns = forceColumns ?? ResponsiveHelper.getGridColumns(context);
    
    return Wrap(
      spacing: spacing,
      runSpacing: runSpacing,
      children: children.map((child) {
        final itemWidth = (ResponsiveHelper.getScreenWidth(context) - 
            (spacing * (columns - 1)) - 32) / columns; // 32 for padding
        
        return SizedBox(
          width: itemWidth,
          child: child,
        );
      }).toList(),
    );
  }
}

/// ويدجت للنص المتجاوب
class ResponsiveText extends StatelessWidget {
  final String text;
  final double baseFontSize;
  final FontWeight? fontWeight;
  final Color? color;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;
  
  const ResponsiveText(
    this.text, {
    super.key,
    required this.baseFontSize,
    this.fontWeight,
    this.color,
    this.textAlign,
    this.maxLines,
    this.overflow,
  });
  
  @override
  Widget build(BuildContext context) {
    return Text(
      text,
      style: TextStyle(
        fontSize: ResponsiveHelper.getFontSize(context, baseFontSize),
        fontWeight: fontWeight,
        color: color,
      ),
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
}

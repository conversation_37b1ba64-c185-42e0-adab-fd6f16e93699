rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // السماح بالقراءة والكتابة للمستخدمين المصادق عليهم
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      // السماح بإنشاء حساب جديد حتى لو لم يتم التحقق من البريد بعد
      allow create: if request.auth != null && request.auth.uid == userId;
    }

    // قواعد للأدمن
    match /admins/{adminId} {
      allow read, write: if request.auth != null &&
        (request.auth.token.email == '<EMAIL>' ||
         resource.data.email == request.auth.token.email);
    }

    // قواعد ملفات PDF - مؤقتاً للاختبار (مفتوحة للجميع)
    match /pdfs/{pdfId} {
      // السماح بالقراءة للجميع مؤقتاً للاختبار
      allow read: if true;

      // السماح بالكتابة للجميع مؤقتاً للاختبار
      allow create, update: if true;

      // السماح بالحذف للجميع مؤقتاً للاختبار
      allow delete: if true;
    }

    // قواعد الإشعارات - محسنة للأمان
    match /notifications/{notificationId} {
      // السماح بالقراءة للمستخدمين المسجلين
      allow read: if request.auth != null;

      // السماح بالكتابة للأدمن فقط
      allow write: if request.auth != null &&
        request.auth.token.email == '<EMAIL>';
    }

    // قواعد المنشورات - محسنة للأمان والأداء
    match /posts/{postId} {
      // السماح بالقراءة للجميع
      allow read: if true;

      // السماح بالإنشاء للمستخدمين المسجلين
      allow create: if request.auth != null &&
        request.resource.data.authorId == request.auth.uid &&
        request.resource.data.keys().hasAll(['content', 'authorId', 'createdAt']);

      // السماح بالتحديث لصاحب المنشور أو الأدمن
      allow update: if request.auth != null &&
        (resource.data.authorId == request.auth.uid ||
         request.auth.token.email == '<EMAIL>');

      // السماح بالحذف لصاحب المنشور أو الأدمن
      allow delete: if request.auth != null &&
        (resource.data.authorId == request.auth.uid ||
         request.auth.token.email == '<EMAIL>');
    }

    // قواعد التعليقات - محسنة
    match /comments/{commentId} {
      allow read: if true;

      allow create: if request.auth != null &&
        request.resource.data.authorId == request.auth.uid;

      allow update, delete: if request.auth != null &&
        (resource.data.authorId == request.auth.uid ||
         request.auth.token.email == '<EMAIL>');
    }

    // قواعد الإعجابات - محسنة
    match /likes/{likeId} {
      allow read: if true;

      allow create, delete: if request.auth != null &&
        request.auth.uid == likeId.split('_')[0];
    }

    // قواعد المشاركات - محسنة
    match /shares/{shareId} {
      allow read: if true;

      allow create: if request.auth != null &&
        request.resource.data.userId == request.auth.uid;
    }

    // قواعد الاستطلاعات - محسنة
    match /polls/{pollId} {
      allow read: if true;

      allow create: if request.auth != null &&
        request.resource.data.createdBy == request.auth.uid;

      allow update, delete: if request.auth != null &&
        (resource.data.createdBy == request.auth.uid ||
         request.auth.token.email == '<EMAIL>');
    }

    // قواعد أصوات الاستطلاعات - محسنة
    match /poll_votes/{voteId} {
      allow read: if request.auth != null;

      allow create, update: if request.auth != null &&
        request.resource.data.userId == request.auth.uid;

      allow delete: if request.auth != null &&
        resource.data.userId == request.auth.uid;
    }

    // قواعد الدردشة - محسنة للأمان والأداء
    match /chats/{chatId} {
      // السماح بالقراءة للمستخدمين المسجلين
      allow read: if request.auth != null;

      // السماح بالكتابة للمستخدمين المسجلين (إنشاء غرف دردشة)
      allow create: if request.auth != null &&
        request.resource.data.keys().hasAll(['name', 'type', 'createdAt']);

      // السماح بالتحديث للأدمن فقط
      allow update: if request.auth != null &&
        request.auth.token.email == '<EMAIL>';

      // منع الحذف (الاحتفاظ بسجل الدردشة)
      allow delete: if false;
    }

    // قواعد رسائل الدردشة - محسنة
    match /chat_messages/{messageId} {
      // السماح بالقراءة للمستخدمين المسجلين
      allow read: if request.auth != null;

      // السماح بالإنشاء للمستخدمين المسجلين
      allow create: if request.auth != null &&
        request.resource.data.senderId == request.auth.uid &&
        request.resource.data.keys().hasAll(['content', 'senderId', 'chatId', 'timestamp']);

      // السماح بالتحديث لصاحب الرسالة (تعديل الرسالة)
      allow update: if request.auth != null &&
        resource.data.senderId == request.auth.uid;

      // السماح بالحذف لصاحب الرسالة أو الأدمن
      allow delete: if request.auth != null &&
        (resource.data.senderId == request.auth.uid ||
         request.auth.token.email == '<EMAIL>');
    }

    // السماح بالقراءة والكتابة للتسجيلات المؤقتة
    match /pending_registrations/{email} {
      allow read, write: if true;
    }

    // قواعد منشورات المجتمع - محسنة للأمان
    match /community_posts/{postId} {
      // السماح بالقراءة للجميع
      allow read: if true;

      // السماح بالإنشاء للمستخدمين المسجلين
      allow create: if request.auth != null &&
        request.resource.data.keys().hasAll(['content', 'authorId', 'authorName', 'timestamp']);

      // السماح بالتحديث لصاحب المنشور أو الأدمن
      allow update: if request.auth != null &&
        (resource.data.authorId == request.auth.uid ||
         request.auth.token.email == '<EMAIL>');

      // السماح بالحذف لصاحب المنشور أو الأدمن
      allow delete: if request.auth != null &&
        (resource.data.authorId == request.auth.uid ||
         request.auth.token.email == '<EMAIL>');
    }

    // قواعد تعليقات المجتمع - محسنة
    match /community_comments/{commentId} {
      // السماح بالقراءة للجميع
      allow read: if true;

      // السماح بالإنشاء للمستخدمين المسجلين
      allow create: if request.auth != null &&
        request.resource.data.keys().hasAll(['content', 'authorId', 'authorName', 'postId', 'timestamp']);

      // السماح بالتحديث لصاحب التعليق أو الأدمن
      allow update: if request.auth != null &&
        (resource.data.authorId == request.auth.uid ||
         request.auth.token.email == '<EMAIL>');

      // السماح بالحذف لصاحب التعليق أو الأدمن
      allow delete: if request.auth != null &&
        (resource.data.authorId == request.auth.uid ||
         request.auth.token.email == '<EMAIL>');
    }

    // قواعد إحصائيات المجتمع - محسنة
    match /community_stats/{statsId} {
      // السماح بالقراءة للجميع
      allow read: if true;

      // السماح بالكتابة للمستخدمين المسجلين (لتحديث الإحصائيات)
      allow write: if request.auth != null;
    }

    // قواعد أصوات الاستطلاعات في المجتمع - محسنة
    match /community_poll_votes/{voteId} {
      // السماح بالقراءة للمستخدمين المسجلين
      allow read: if request.auth != null;

      // السماح بالإنشاء والتحديث للمستخدمين المسجلين
      allow create, update: if request.auth != null &&
        request.resource.data.userId == request.auth.uid;

      // السماح بالحذف لصاحب الصوت
      allow delete: if request.auth != null &&
        resource.data.userId == request.auth.uid;
    }

    // قواعد FCM Tokens - لحفظ رموز الإشعارات
    match /fcm_tokens/{tokenId} {
      // السماح بالقراءة للأدمن فقط
      allow read: if request.auth != null &&
        request.auth.token.email == '<EMAIL>';

      // السماح بالكتابة للمستخدمين المسجلين (لحفظ رموزهم)
      allow create, update: if request.auth != null &&
        request.resource.data.userId == request.auth.uid;

      // السماح بالحذف لصاحب الرمز أو الأدمن
      allow delete: if request.auth != null &&
        (resource.data.userId == request.auth.uid ||
         request.auth.token.email == '<EMAIL>');
    }

    // قواعد User Tokens - لحفظ رموز الإشعارات (الاسم المستخدم في الكود)
    match /user_tokens/{tokenId} {
      // السماح بالقراءة للأدمن فقط
      allow read: if request.auth != null &&
        request.auth.token.email == '<EMAIL>';

      // السماح بالكتابة للمستخدمين المسجلين (لحفظ رموزهم)
      allow create, update: if request.auth != null;

      // السماح بالحذف لصاحب الرمز أو الأدمن
      allow delete: if request.auth != null;
    }

    // قواعد إعدادات التطبيق - للأدمن فقط
    match /app_settings/{settingId} {
      // السماح بالقراءة للجميع
      allow read: if true;

      // السماح بالكتابة للأدمن فقط
      allow write: if request.auth != null &&
        request.auth.token.email == '<EMAIL>';
    }

    // قواعد الإحصائيات العامة - للقراءة العامة والكتابة للأدمن
    match /app_stats/{statId} {
      // السماح بالقراءة للجميع
      allow read: if true;

      // السماح بالكتابة للأدمن فقط
      allow write: if request.auth != null &&
        request.auth.token.email == '<EMAIL>';
    }

    // قواعد التسجيل المؤقت - للتحقق من البريد الإلكتروني
    match /pending_registrations/{email} {
      // السماح بالكتابة للجميع (لحفظ بيانات التسجيل المؤقتة)
      allow create, update: if true;

      // السماح بالقراءة للجميع (للتحقق من الرابط)
      allow read: if true;

      // السماح بالحذف للجميع (لحذف البيانات بعد التسجيل)
      allow delete: if true;
    }

    // قواعد كودات التحقق - للنظام البديل
    match /verification_codes/{email} {
      // السماح بالكتابة للجميع (لحفظ كودات التحقق)
      allow create, update: if true;

      // السماح بالقراءة للجميع (للتحقق من الكود)
      allow read: if true;

      // السماح بالحذف للجميع (لحذف الكود بعد الاستخدام)
      allow delete: if true;
    }

    // قواعد عامة للمجموعات الفرعية غير المحددة - أمان إضافي
    match /{document=**} {
      // منع الوصول لأي مجموعة غير محددة صراحة
      allow read, write: if false;
    }
  }
}

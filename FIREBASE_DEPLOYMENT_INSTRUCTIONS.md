# تعليمات نشر قواعد Firebase وإضافة الفهارس

## 🔥 **خطوات نشر قواعد Firestore**

### 1. تسجيل الدخول إلى Firebase CLI
```bash
firebase login
```

### 2. تهيئة المشروع (إذا لم يتم من قبل)
```bash
firebase init
```
اختر:
- Firestore: Configure security rules and indexes files
- Realtime Database: Configure a security rules file

### 3. نشر قواعد Firestore
```bash
firebase deploy --only firestore:rules
```

### 4. نشر قواعد Realtime Database
```bash
firebase deploy --only database
```

---

## 📊 **إضافة الفهارس المطلوبة يدوياً**

### فهارس Firebase Realtime Database

يجب إضافة الفهارس التالية في Firebase Console:

#### 1. فهرس رسائل الدردشة العامة
- **المسار**: `/chats/general/messages`
- **الفهرس**: `timestamp`

#### 2. فهرس رسائل الفرقة الأولى
- **المسار**: `/chats/first_year/messages`
- **الفهرس**: `timestamp`

#### 3. فهرس رسائل الفرقة الثانية
- **المسار**: `/chats/second_year/messages`
- **الفهرس**: `timestamp`

#### 4. فهرس رسائل الفرقة الثالثة
- **المسار**: `/chats/third_year/messages`
- **الفهرس**: `timestamp`

#### 5. فهرس رسائل الفرقة الرابعة
- **المسار**: `/chats/fourth_year/messages`
- **الفهرس**: `timestamp`

### خطوات إضافة الفهارس:

1. اذهب إلى [Firebase Console](https://console.firebase.google.com)
2. اختر مشروعك
3. اذهب إلى **Realtime Database**
4. اختر تبويب **Rules**
5. انقر على **Add Index**
6. أضف كل فهرس من القائمة أعلاه

أو يمكنك إضافة الفهارس مباشرة في ملف القواعد كما هو موضح في `database.rules.json`.

---

## 🔐 **التحقق من نشر القواعد**

### 1. تحقق من قواعد Firestore
```bash
firebase firestore:rules:get
```

### 2. تحقق من قواعد Realtime Database
```bash
firebase database:get /.settings/rules
```

---

## ⚠️ **ملاحظات مهمة**

### قبل النشر:
1. **تأكد من أن البريد الإلكتروني للأدمن صحيح**: `<EMAIL>`
2. **اختبر القواعد في بيئة التطوير أولاً**
3. **تأكد من أن جميع المستخدمين مسجلين بشكل صحيح**

### بعد النشر:
1. **اختبر إنشاء منشور جديد**
2. **اختبر إرسال رسالة في الدردشة**
3. **اختبر حفظ FCM Token**
4. **تحقق من عدم ظهور أخطاء permission-denied**

---

## 🚨 **حل المشاكل الشائعة**

### مشكلة: `permission-denied` في Firestore
**الحل**: تأكد من أن:
- المستخدم مسجل دخول (`auth != null`)
- البريد الإلكتروني للأدمن صحيح
- القواعد تم نشرها بنجاح

### مشكلة: `index-not-defined` في Realtime Database
**الحل**: 
- أضف الفهارس المطلوبة كما هو موضح أعلاه
- انتظر بضع دقائق حتى يتم تطبيق الفهارس

### مشكلة: بطء في تحميل الرسائل
**الحل**:
- تأكد من إضافة فهرس `timestamp` لجميع مسارات الرسائل
- قلل عدد الرسائل المحملة في المرة الواحدة

---

## 📝 **أوامر مفيدة**

```bash
# عرض حالة المشروع
firebase projects:list

# عرض القواعد الحالية
firebase firestore:rules:get

# نشر جميع القواعد
firebase deploy --only firestore:rules,database

# مراقبة السجلات
firebase functions:log
```

---

## ✅ **قائمة التحقق النهائية**

- [ ] تم نشر قواعد Firestore
- [ ] تم نشر قواعد Realtime Database  
- [ ] تم إضافة جميع الفهارس المطلوبة
- [ ] تم اختبار إنشاء منشور جديد
- [ ] تم اختبار إرسال رسالة
- [ ] تم اختبار حفظ الإشعارات
- [ ] لا توجد أخطاء permission-denied
- [ ] لا توجد أخطاء index-not-defined

بعد إكمال جميع هذه الخطوات، يجب أن تختفي جميع مشاكل الصلاحيات والفهارس من التطبيق.

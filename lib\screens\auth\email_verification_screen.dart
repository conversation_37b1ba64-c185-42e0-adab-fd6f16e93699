import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../providers/auth_provider.dart';
import '../../providers/theme_provider.dart';
import '../../services/firebase_auth_email_service.dart';
import '../../widgets/glass_morphism.dart';

/// صفحة التحقق من البريد الإلكتروني الحقيقية (مثل Facebook)
class EmailVerificationScreen extends StatefulWidget {
  final String email;
  final String password;
  final String displayName;

  const EmailVerificationScreen({
    super.key,
    required this.email,
    required this.password,
    required this.displayName,
  });

  @override
  State<EmailVerificationScreen> createState() =>
      _EmailVerificationScreenState();
}

class _EmailVerificationScreenState extends State<EmailVerificationScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _pulseController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _pulseAnimation;

  bool _isChecking = false;
  bool _isResending = false;
  String _message = '';
  bool _isSuccess = false;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _animationController.forward();
    _pulseController.repeat(reverse: true);

    // بدء التحقق التلقائي كل 3 ثوانٍ
    _startAutoCheck();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  /// بدء التحقق التلقائي من حالة التحقق
  void _startAutoCheck() {
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted && !_isSuccess) {
        _checkVerificationStatus();
        _startAutoCheck(); // إعادة التحقق
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<AuthProvider, ThemeProvider>(
      builder: (context, authProvider, themeProvider, child) {
        return Scaffold(
          body: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  const Color(0xFF667EEA),
                  const Color(0xFF764BA2),
                  const Color(0xFF6366F1),
                ],
              ),
            ),
            child: SafeArea(
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: Padding(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      children: [
                        _buildHeader(),
                        Expanded(
                          child: Center(
                            child: SingleChildScrollView(
                              child: _buildVerificationCard(),
                            ),
                          ),
                        ),
                        _buildFooter(),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.arrow_back_ios, color: Colors.white, size: 24),
        ),
        Expanded(
          child: Text(
            'التحقق من البريد الإلكتروني',
            style: GoogleFonts.cairo(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        const SizedBox(width: 48), // للتوازن
      ],
    );
  }

  Widget _buildVerificationCard() {
    return GlassMorphism(
      blur: 20,
      opacity: 0.2,
      child: Container(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildIcon(),
            const SizedBox(height: 24),
            _buildTitle(),
            const SizedBox(height: 16),
            _buildDescription(),
            const SizedBox(height: 32),
            _buildEmailInfo(),
            const SizedBox(height: 24),
            _buildMessage(),
            const SizedBox(height: 24),
            _buildButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildIcon() {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color:
                  _isSuccess
                      ? Colors.green.withValues(alpha: 0.2)
                      : Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.white.withValues(alpha: 0.1),
                  blurRadius: 20,
                  spreadRadius: 5,
                ),
              ],
            ),
            child: Icon(
              _isSuccess ? Icons.check_circle : Icons.email,
              size: 40,
              color: _isSuccess ? Colors.green : Colors.white,
            ),
          ),
        );
      },
    );
  }

  Widget _buildTitle() {
    return Text(
      _isSuccess ? 'تم التحقق بنجاح!' : 'تحقق من بريدك الإلكتروني',
      style: GoogleFonts.cairo(
        fontSize: 24,
        fontWeight: FontWeight.bold,
        color: Colors.white,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildDescription() {
    return Text(
      _isSuccess
          ? 'تم التحقق من بريدك الإلكتروني بنجاح! يمكنك الآن الدخول إلى التطبيق.'
          : 'لقد أرسلنا رابط التحقق إلى بريدك الإلكتروني. يرجى فتح البريد والنقر على الرابط لتفعيل حسابك.',
      style: GoogleFonts.cairo(
        fontSize: 16,
        color: Colors.white.withValues(alpha: 0.9),
        height: 1.5,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildEmailInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.email_outlined,
            color: Colors.white.withValues(alpha: 0.8),
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              widget.email,
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.white.withValues(alpha: 0.9),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessage() {
    if (_message.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color:
            _isSuccess
                ? Colors.green.withValues(alpha: 0.2)
                : Colors.orange.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color:
              _isSuccess
                  ? Colors.green.withValues(alpha: 0.5)
                  : Colors.orange.withValues(alpha: 0.5),
          width: 1,
        ),
      ),
      child: Text(
        _message,
        style: GoogleFonts.cairo(
          fontSize: 14,
          color: Colors.white.withValues(alpha: 0.9),
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildButtons() {
    return Column(
      children: [
        // زر التحقق اليدوي
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _isChecking ? null : _checkVerificationStatus,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white.withValues(alpha: 0.2),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child:
                _isChecking
                    ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                    : Text(
                      'تحقق من التفعيل',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
          ),
        ),
        const SizedBox(height: 16),
        // زر إعادة الإرسال
        TextButton(
          onPressed: _isResending ? null : _resendVerificationEmail,
          child:
              _isResending
                  ? const SizedBox(
                    height: 16,
                    width: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                  : Text(
                    'إعادة إرسال البريد',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: Colors.white.withValues(alpha: 0.8),
                      decoration: TextDecoration.underline,
                    ),
                  ),
        ),
      ],
    );
  }

  Widget _buildFooter() {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.symmetric(horizontal: 20),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.3), width: 1),
      ),
      child: Column(
        children: [
          Icon(Icons.info_outline, color: Colors.blue, size: 24),
          const SizedBox(height: 8),
          Text(
            '💡 نصيحة',
            style: GoogleFonts.cairo(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.blue,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'تحقق من مجلد الرسائل غير المرغوب فيها (Spam) إذا لم تجد الرسالة في البريد الوارد.',
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: Colors.white.withValues(alpha: 0.9),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// التحقق من حالة التحقق من البريد الإلكتروني
  Future<void> _checkVerificationStatus() async {
    if (_isChecking) return;

    setState(() {
      _isChecking = true;
      _message = 'جاري التحقق...';
    });

    try {
      final isVerified = await FirebaseAuthEmailService.checkEmailVerification(
        widget.email,
        widget.password,
      );

      if (isVerified) {
        setState(() {
          _isSuccess = true;
          _message = 'تم التحقق من بريدك الإلكتروني بنجاح!';
        });

        // الانتقال إلى الصفحة الرئيسية بعد ثانيتين
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted) {
            Navigator.of(
              context,
            ).pushNamedAndRemoveUntil('/home', (route) => false);
          }
        });
      } else {
        setState(() {
          _message =
              'لم يتم التحقق بعد. يرجى فتح البريد والنقر على رابط التفعيل.';
        });
      }
    } catch (e) {
      setState(() {
        _message = 'خطأ في التحقق: $e';
      });
    } finally {
      setState(() {
        _isChecking = false;
      });
    }
  }

  /// إعادة إرسال بريد التحقق
  Future<void> _resendVerificationEmail() async {
    if (_isResending) return;

    setState(() {
      _isResending = true;
      _message = 'جاري إعادة الإرسال...';
    });

    try {
      final success = await FirebaseAuthEmailService.resendVerificationEmail(
        widget.email,
        widget.password,
      );

      if (success) {
        setState(() {
          _message = 'تم إعادة إرسال بريد التحقق بنجاح!';
        });
      } else {
        setState(() {
          _message = 'فشل في إعادة الإرسال. يرجى المحاولة مرة أخرى.';
        });
      }
    } catch (e) {
      setState(() {
        _message = 'خطأ في إعادة الإرسال: $e';
      });
    } finally {
      setState(() {
        _isResending = false;
      });
    }
  }
}

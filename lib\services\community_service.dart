import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/community_post.dart';
import '../utils/logger.dart';
import 'smart_cache_service.dart';
import 'network_service.dart';

class CommunityService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  // Collections
  static const String _postsCollection = 'community_posts';
  static const String _commentsCollection = 'community_comments';
  static const String _statsCollection = 'community_stats';
  static const String _usersCollection = 'users';

  // Pagination settings
  static const int _postsPerPage = 10;
  static const int _commentsPerPage = 20;

  // Get current user
  static User? get currentUser => _auth.currentUser;
  static String get currentUserId => currentUser?.uid ?? 'anonymous';
  static String get currentUserName {
    if (currentUser == null) return 'مستخدم مجهول';

    // استخدام البريد الإلكتروني إذا لم يكن هناك اسم عرض
    final displayName = currentUser!.displayName;
    if (displayName != null && displayName.isNotEmpty) {
      return displayName;
    }

    final email = currentUser!.email;
    if (email != null && email.isNotEmpty) {
      // استخراج الجزء الأول من البريد الإلكتروني
      return email.split('@')[0];
    }

    return 'مستخدم مجهول';
  }

  /// إنشاء منشور جديد
  static Future<String?> createPost({
    required String content,
    List<String> imageUrls = const [],
    Map<String, dynamic>? poll,
    List<String> tags = const [],
    String category = 'عام',
  }) async {
    try {
      // التحقق من تسجيل الدخول
      if (currentUser == null) {
        AppLogger.error('المستخدم غير مسجل الدخول', 'CommunityService');
        return null;
      }

      // التحقق من حد النص (3000 حرف)
      if (content.length > 3000) {
        AppLogger.error(
          'النص يتجاوز الحد المسموح (3000 حرف)',
          'CommunityService',
        );
        return null;
      }
      final post = CommunityPost(
        id: '', // سيتم تعيينه من Firebase
        authorId: currentUserId,
        authorName: currentUserName,
        content: content,
        timestamp: DateTime.now(),
        imageUrls: imageUrls,
        poll: poll,
        tags: tags,
        category: category,
      );

      final docRef = await NetworkService.executeWithRetry(
        () async =>
            await _firestore.collection(_postsCollection).add(post.toMap()),
        maxRetries: 3,
        useCache: false, // لا نحتاج cache للكتابة
      );

      // تحديث الإحصائيات
      await _updateStats();

      AppLogger.info('تم إنشاء منشور جديد: ${docRef.id}', 'CommunityService');
      return docRef.id;
    } catch (e) {
      AppLogger.error('خطأ في إنشاء المنشور', 'CommunityService', e);
      return null;
    }
  }

  /// الحصول على المنشورات - محسن للأداء مع pagination
  static Stream<List<CommunityPost>> getPostsStream({
    String? category,
    int limit = 10, // تقليل العدد لتحسين الأداء
    DocumentSnapshot? lastDocument,
  }) {
    try {
      Query query = _firestore
          .collection(_postsCollection)
          .orderBy('timestamp', descending: true);

      if (category != null && category != 'الكل') {
        query = query.where('category', isEqualTo: category);
      }

      // إضافة pagination للأداء الأفضل
      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }

      query = query.limit(limit);

      return query.snapshots().asyncMap((snapshot) async {
        final posts = <CommunityPost>[];

        for (final doc in snapshot.docs) {
          try {
            // محاولة الحصول على المنشور من Cache أولاً
            final cacheKey = 'post_${doc.id}';
            CommunityPost? cachedPost =
                await SmartCacheService.get<CommunityPost>(cacheKey);

            if (cachedPost != null) {
              posts.add(cachedPost);
              continue;
            }

            // إنشاء المنشور من البيانات الجديدة
            final post = CommunityPost.fromMap(
              doc.data() as Map<String, dynamic>,
              doc.id,
            );

            // حفظ في Cache لمدة 15 دقيقة
            await SmartCacheService.set(
              cacheKey,
              post,
              ttlMinutes: 15,
              persistToDisk: true,
            );

            posts.add(post);
          } catch (e) {
            AppLogger.error(
              'خطأ في معالجة منشور: ${doc.id}',
              'CommunityService',
              e,
            );
          }
        }

        // ترتيب المنشورات: المثبتة أولاً، ثم حسب التاريخ
        posts.sort((a, b) {
          if (a.isPinned && !b.isPinned) return -1;
          if (!a.isPinned && b.isPinned) return 1;
          return b.timestamp.compareTo(a.timestamp);
        });

        return posts;
      });
    } catch (e) {
      AppLogger.error('خطأ في جلب المنشورات', 'CommunityService', e);
      return Stream.value([]);
    }
  }

  /// الحصول على المزيد من المنشورات (للـ pagination)
  static Future<List<CommunityPost>> getMorePosts({
    String? category,
    required DocumentSnapshot lastDocument,
    int limit = 10,
  }) async {
    try {
      Query query = _firestore
          .collection(_postsCollection)
          .orderBy('timestamp', descending: true);

      if (category != null && category != 'الكل') {
        query = query.where('category', isEqualTo: category);
      }

      query = query.startAfterDocument(lastDocument).limit(limit);

      final snapshot = await query.get();
      final posts = <CommunityPost>[];

      for (final doc in snapshot.docs) {
        try {
          // محاولة الحصول على المنشور من Cache أولاً
          final cacheKey = 'post_${doc.id}';
          CommunityPost? cachedPost =
              await SmartCacheService.get<CommunityPost>(cacheKey);

          if (cachedPost != null) {
            posts.add(cachedPost);
            continue;
          }

          // إنشاء المنشور من البيانات الجديدة
          final data = doc.data() as Map<String, dynamic>;
          final post = CommunityPost.fromMap(data, doc.id);

          // حفظ في Cache لمدة 15 دقيقة
          await SmartCacheService.set(
            cacheKey,
            post,
            ttlMinutes: 15,
            persistToDisk: true,
          );

          posts.add(post);
        } catch (e) {
          AppLogger.error(
            'خطأ في معالجة منشور: ${doc.id}',
            'CommunityService',
            e,
          );
        }
      }

      return posts;
    } catch (e) {
      AppLogger.error('خطأ في جلب المزيد من المنشورات', 'CommunityService', e);
      return [];
    }
  }

  /// إضافة إعجاب للمنشور - محسن للسرعة القصوى
  static Future<bool> toggleLike(String postId) async {
    try {
      // التحقق من تسجيل الدخول
      if (currentUser == null) {
        AppLogger.error('المستخدم غير مسجل الدخول', 'CommunityService');
        return false;
      }

      final postRef = _firestore.collection(_postsCollection).doc(postId);

      // استخدام update مباشر بدون انتظار للسرعة القصوى
      postRef
          .update({
            'likedBy': FieldValue.arrayUnion([currentUserId]),
          })
          .catchError((error) {
            // إذا فشل الإضافة، جرب الإزالة
            postRef
                .update({
                  'likedBy': FieldValue.arrayRemove([currentUserId]),
                })
                .catchError((e) {
                  AppLogger.error(
                    'خطأ في إزالة الإعجاب',
                    'CommunityService',
                    e,
                  );
                });
          });

      return true;
    } catch (e) {
      AppLogger.error('خطأ في تبديل الإعجاب', 'CommunityService', e);
      return false;
    }
  }

  /// تثبيت أو إلغاء تثبيت المنشور (للأدمن فقط)
  static Future<bool> togglePinPost(String postId) async {
    try {
      // التحقق من تسجيل الدخول
      if (currentUser == null) {
        AppLogger.error('المستخدم غير مسجل الدخول', 'CommunityService');
        return false;
      }

      // التحقق من صلاحيات الأدمن
      if (!isAdmin) {
        AppLogger.error('المستخدم ليس أدمن', 'CommunityService');
        return false;
      }

      final postRef = _firestore.collection(_postsCollection).doc(postId);
      final postDoc = await postRef.get();

      if (!postDoc.exists) {
        AppLogger.error('المنشور غير موجود', 'CommunityService');
        return false;
      }

      final currentPinnedStatus = postDoc.data()?['isPinned'] ?? false;

      await postRef.update({'isPinned': !currentPinnedStatus});

      AppLogger.success(
        currentPinnedStatus ? 'تم إلغاء تثبيت المنشور' : 'تم تثبيت المنشور',
        'CommunityService',
      );

      return true;
    } catch (e) {
      AppLogger.error('خطأ في تثبيت المنشور', 'CommunityService', e);
      return false;
    }
  }

  /// التحقق من صلاحيات الأدمن
  static bool get isAdmin {
    return currentUser?.email == '<EMAIL>';
  }

  /// إضافة تعليق أو رد
  static Future<String?> addComment({
    required String postId,
    required String content,
    String? parentCommentId,
    String? replyToUserId,
    String? replyToUserName,
  }) async {
    try {
      // التحقق من تسجيل الدخول
      if (currentUser == null) {
        AppLogger.error('المستخدم غير مسجل الدخول', 'CommunityService');
        return null;
      }
      final comment = CommunityComment(
        id: '',
        postId: postId,
        authorId: currentUserId,
        authorName: currentUserName,
        content: content,
        timestamp: DateTime.now(),
        parentCommentId: parentCommentId,
        replyToUserId: replyToUserId,
        replyToUserName: replyToUserName,
      );

      final docRef = await NetworkService.executeWithRetry(
        () async => await _firestore
            .collection(_commentsCollection)
            .add(comment.toMap()),
        maxRetries: 3,
        useCache: false,
      );

      // تحديث الإحصائيات بشكل غير متزامن (لا ننتظر)
      _updateStats().catchError((e) {
        AppLogger.error('خطأ في تحديث الإحصائيات', 'CommunityService', e);
      });

      AppLogger.info(
        'تم إضافة تعليق جديد: ${docRef.id} للمنشور: $postId',
        'CommunityService',
      );
      return docRef.id;
    } catch (e) {
      AppLogger.error('خطأ في إضافة التعليق', 'CommunityService', e);
      return null;
    }
  }

  /// الحصول على التعليقات مع الردود منظمة
  static Stream<List<CommunityComment>> getCommentsStream(String postId) {
    try {
      return _firestore
          .collection(_commentsCollection)
          .where('postId', isEqualTo: postId)
          .snapshots()
          .map((snapshot) {
            final allComments =
                snapshot.docs.map((doc) {
                  final data = doc.data();
                  return CommunityComment.fromMap(data, doc.id);
                }).toList();

            // تنظيم التعليقات والردود بدون ترتيب مسبق
            final organizedComments = _organizeCommentsWithReplies(allComments);

            // ترتيب التعليقات الرئيسية فقط حسب التاريخ (الأحدث أولاً)
            organizedComments.sort(
              (a, b) => b.timestamp.compareTo(a.timestamp),
            );

            return organizedComments;
          });
    } catch (e) {
      AppLogger.error('خطأ في جلب التعليقات', 'CommunityService', e);
      return Stream.value([]);
    }
  }

  /// تنظيم التعليقات والردود في هيكل هرمي
  static List<CommunityComment> _organizeCommentsWithReplies(
    List<CommunityComment> allComments,
  ) {
    final Map<String, List<CommunityComment>> repliesMap = {};
    final List<CommunityComment> mainComments = [];

    // فصل التعليقات الرئيسية عن الردود
    for (final comment in allComments) {
      final hasParent =
          comment.parentCommentId != null &&
          comment.parentCommentId!.isNotEmpty &&
          comment.parentCommentId != 'null';

      if (!hasParent) {
        mainComments.add(comment);
      } else {
        repliesMap.putIfAbsent(comment.parentCommentId!, () => []);
        repliesMap[comment.parentCommentId!]!.add(comment);
      }
    }

    // ترتيب الردود حسب التاريخ
    for (final replies in repliesMap.values) {
      replies.sort((a, b) => a.timestamp.compareTo(b.timestamp));
    }

    // إضافة الردود للتعليقات الرئيسية
    final organizedComments =
        mainComments.map((comment) {
          final replies = repliesMap[comment.id] ?? [];

          return CommunityComment(
            id: comment.id,
            postId: comment.postId,
            authorId: comment.authorId,
            authorName: comment.authorName,
            content: comment.content,
            timestamp: comment.timestamp,
            likedBy: comment.likedBy,
            parentCommentId: comment.parentCommentId,
            replyToUserId: comment.replyToUserId,
            replyToUserName: comment.replyToUserName,
            replies: replies,
          );
        }).toList();

    return organizedComments;
  }

  /// الحصول على عدد التعليقات
  static Future<int> getCommentsCount(String postId) async {
    try {
      final snapshot =
          await _firestore
              .collection(_commentsCollection)
              .where('postId', isEqualTo: postId)
              .get();

      return snapshot.docs.length;
    } catch (e) {
      AppLogger.error('خطأ في جلب عدد التعليقات', 'CommunityService', e);
      return 0;
    }
  }

  /// التصويت في استطلاع
  static Future<bool> voteInPoll({
    required String postId,
    required int optionIndex,
  }) async {
    try {
      // التحقق من تسجيل الدخول
      if (currentUser == null) {
        AppLogger.error('المستخدم غير مسجل الدخول', 'CommunityService');
        return false;
      }

      final userId = currentUserId;
      final voteId = '${postId}_$userId';

      // التحقق من وجود صوت سابق
      final existingVote =
          await _firestore.collection('community_poll_votes').doc(voteId).get();

      if (existingVote.exists) {
        // تحديث الصوت الموجود
        await _firestore.collection('community_poll_votes').doc(voteId).update({
          'optionIndex': optionIndex,
          'timestamp': FieldValue.serverTimestamp(),
        });
      } else {
        // إضافة صوت جديد
        await _firestore.collection('community_poll_votes').doc(voteId).set({
          'postId': postId,
          'userId': userId,
          'optionIndex': optionIndex,
          'timestamp': FieldValue.serverTimestamp(),
        });
      }

      // تحديث عدد الأصوات في المنشور
      await _updatePollVotes(postId);

      AppLogger.info('تم التصويت في الاستطلاع: $postId', 'CommunityService');
      return true;
    } catch (e) {
      AppLogger.error('خطأ في التصويت', 'CommunityService', e);
      return false;
    }
  }

  /// تحديث أصوات الاستطلاع
  static Future<void> _updatePollVotes(String postId) async {
    try {
      // جلب جميع الأصوات للمنشور
      final votesSnapshot =
          await _firestore
              .collection('community_poll_votes')
              .where('postId', isEqualTo: postId)
              .get();

      // حساب الأصوات لكل خيار
      final Map<String, int> votes = {};
      for (var doc in votesSnapshot.docs) {
        final data = doc.data();
        final optionIndex = data['optionIndex'].toString();
        votes[optionIndex] = (votes[optionIndex] ?? 0) + 1;
      }

      // تحديث المنشور بالأصوات الجديدة
      await _firestore.collection(_postsCollection).doc(postId).update({
        'poll.votes': votes,
      });

      AppLogger.info('تم تحديث أصوات الاستطلاع: $postId', 'CommunityService');
    } catch (e) {
      AppLogger.error('خطأ في تحديث أصوات الاستطلاع', 'CommunityService', e);
    }
  }

  /// تبديل الإعجاب بالتعليق - مُصحح
  static Future<bool> toggleCommentLike(String commentId) async {
    try {
      // التحقق من تسجيل الدخول
      if (currentUser == null) {
        AppLogger.error('المستخدم غير مسجل الدخول', 'CommunityService');
        return false;
      }

      final commentRef = _firestore
          .collection(_commentsCollection)
          .doc(commentId);

      // جلب التعليق أولاً للتحقق من حالة الإعجاب
      final commentDoc = await commentRef.get();
      if (!commentDoc.exists) {
        AppLogger.error('التعليق غير موجود', 'CommunityService');
        return false;
      }

      final commentData = commentDoc.data()!;
      final likedBy = List<String>.from(commentData['likedBy'] ?? []);
      final isLiked = likedBy.contains(currentUserId);

      // تبديل الإعجاب بناءً على الحالة الحالية
      if (isLiked) {
        // إزالة الإعجاب
        await commentRef.update({
          'likedBy': FieldValue.arrayRemove([currentUserId]),
        });
        AppLogger.info(
          'تم إزالة الإعجاب من التعليق: $commentId',
          'CommunityService',
        );
      } else {
        // إضافة الإعجاب
        await commentRef.update({
          'likedBy': FieldValue.arrayUnion([currentUserId]),
        });
        AppLogger.info(
          'تم إضافة الإعجاب للتعليق: $commentId',
          'CommunityService',
        );
      }

      return true;
    } catch (e) {
      AppLogger.error('خطأ في تبديل إعجاب التعليق', 'CommunityService', e);
      return false;
    }
  }

  /// تبديل الإعجاب بالمنشور
  static Future<bool> togglePostLike(String postId) async {
    try {
      // التحقق من تسجيل الدخول
      if (currentUser == null) {
        AppLogger.error('المستخدم غير مسجل الدخول', 'CommunityService');
        return false;
      }

      final postRef = _firestore.collection(_postsCollection).doc(postId);

      // جلب المنشور أولاً للتحقق من حالة الإعجاب
      final postDoc = await postRef.get();
      if (!postDoc.exists) {
        AppLogger.error('المنشور غير موجود', 'CommunityService');
        return false;
      }

      final postData = postDoc.data()!;
      final likedBy = List<String>.from(postData['likedBy'] ?? []);
      final isLiked = likedBy.contains(currentUserId);

      // تبديل الإعجاب بناءً على الحالة الحالية
      if (isLiked) {
        // إزالة الإعجاب
        await postRef.update({
          'likedBy': FieldValue.arrayRemove([currentUserId]),
        });
        AppLogger.info(
          'تم إزالة الإعجاب من المنشور: $postId',
          'CommunityService',
        );
      } else {
        // إضافة الإعجاب
        await postRef.update({
          'likedBy': FieldValue.arrayUnion([currentUserId]),
        });
        AppLogger.info('تم إضافة الإعجاب للمنشور: $postId', 'CommunityService');
      }

      return true;
    } catch (e) {
      AppLogger.error('خطأ في تبديل إعجاب المنشور', 'CommunityService', e);
      return false;
    }
  }

  /// حذف تعليق
  static Future<bool> deleteComment(String commentId) async {
    try {
      // التحقق من تسجيل الدخول
      if (currentUser == null) {
        AppLogger.error('المستخدم غير مسجل الدخول', 'CommunityService');
        return false;
      }

      // جلب التعليق للتحقق من الصلاحيات
      final commentDoc =
          await _firestore.collection(_commentsCollection).doc(commentId).get();

      if (!commentDoc.exists) {
        AppLogger.error('التعليق غير موجود', 'CommunityService');
        return false;
      }

      final data = commentDoc.data() as Map<String, dynamic>;
      final authorId = data['authorId'] as String;

      // التحقق من الصلاحيات (صاحب التعليق أو الأدمن)
      if (authorId != currentUserId &&
          currentUser!.email != '<EMAIL>') {
        AppLogger.error('لا توجد صلاحية لحذف هذا التعليق', 'CommunityService');
        return false;
      }

      // حذف التعليق
      await _firestore.collection(_commentsCollection).doc(commentId).delete();

      // حذف الردود المرتبطة بهذا التعليق
      final repliesSnapshot =
          await _firestore
              .collection(_commentsCollection)
              .where('parentCommentId', isEqualTo: commentId)
              .get();

      for (var doc in repliesSnapshot.docs) {
        await doc.reference.delete();
      }

      AppLogger.info('تم حذف التعليق: $commentId', 'CommunityService');
      return true;
    } catch (e) {
      AppLogger.error('خطأ في حذف التعليق', 'CommunityService', e);
      return false;
    }
  }

  /// حذف المنشورات القديمة (أكثر من 6 شهور)
  static Future<int> deleteOldPosts() async {
    try {
      // حساب التاريخ قبل 6 شهور
      final sixMonthsAgo = DateTime.now().subtract(const Duration(days: 180));

      // البحث عن المنشورات القديمة
      final oldPostsSnapshot =
          await _firestore
              .collection(_postsCollection)
              .where('timestamp', isLessThan: Timestamp.fromDate(sixMonthsAgo))
              .get();

      int deletedCount = 0;

      // حذف كل منشور قديم مع التعليقات والأصوات المرتبطة به
      for (var postDoc in oldPostsSnapshot.docs) {
        await _deletePostWithRelatedData(postDoc.id);
        deletedCount++;
      }

      if (deletedCount > 0) {
        AppLogger.info('تم حذف $deletedCount منشور قديم', 'CommunityService');
      }

      return deletedCount;
    } catch (e) {
      AppLogger.error('خطأ في حذف المنشورات القديمة', 'CommunityService', e);
      return 0;
    }
  }

  /// حذف منشور مع جميع البيانات المرتبطة به
  static Future<void> _deletePostWithRelatedData(String postId) async {
    try {
      // حذف التعليقات المرتبطة بالمنشور
      final commentsSnapshot =
          await _firestore
              .collection(_commentsCollection)
              .where('postId', isEqualTo: postId)
              .get();

      for (var doc in commentsSnapshot.docs) {
        await doc.reference.delete();
      }

      // حذف أصوات الاستطلاعات المرتبطة بالمنشور
      final votesSnapshot =
          await _firestore
              .collection('community_poll_votes')
              .where('postId', isEqualTo: postId)
              .get();

      for (var doc in votesSnapshot.docs) {
        await doc.reference.delete();
      }

      // حذف المنشور نفسه
      await _firestore.collection(_postsCollection).doc(postId).delete();
    } catch (e) {
      AppLogger.error('خطأ في حذف المنشور $postId', 'CommunityService', e);
    }
  }

  /// حذف منشور
  static Future<bool> deletePost(String postId) async {
    try {
      // التحقق من تسجيل الدخول
      if (currentUser == null) {
        AppLogger.error('المستخدم غير مسجل الدخول', 'CommunityService');
        return false;
      }

      await _deletePostWithRelatedData(postId);

      // تحديث الإحصائيات
      await _updateStats();

      AppLogger.info('تم حذف المنشور: $postId', 'CommunityService');
      return true;
    } catch (e) {
      AppLogger.error('خطأ في حذف المنشور', 'CommunityService', e);
      return false;
    }
  }

  /// الحصول على الإحصائيات
  static Stream<CommunityStats> getStatsStream() {
    try {
      return _firestore
          .collection(_statsCollection)
          .doc('global')
          .snapshots()
          .map((doc) {
            if (doc.exists) {
              return CommunityStats.fromMap(doc.data() as Map<String, dynamic>);
            } else {
              return CommunityStats(
                totalUsers: 0,
                activeUsers: 0,
                totalPosts: 0,
                todayPosts: 0,
                totalComments: 0,
                todayComments: 0,
                lastUpdated: DateTime.now(),
              );
            }
          });
    } catch (e) {
      AppLogger.error('خطأ في جلب الإحصائيات', 'CommunityService', e);
      return Stream.value(
        CommunityStats(
          totalUsers: 0,
          activeUsers: 0,
          totalPosts: 0,
          todayPosts: 0,
          totalComments: 0,
          todayComments: 0,
          lastUpdated: DateTime.now(),
        ),
      );
    }
  }

  /// تحديث الإحصائيات
  static Future<void> _updateStats() async {
    try {
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);

      // حساب إجمالي المنشورات
      final totalPostsSnapshot =
          await _firestore.collection(_postsCollection).get();
      final totalPosts = totalPostsSnapshot.docs.length;

      // حساب منشورات اليوم
      final todayPostsSnapshot =
          await _firestore
              .collection(_postsCollection)
              .where(
                'timestamp',
                isGreaterThanOrEqualTo: Timestamp.fromDate(today),
              )
              .get();
      final todayPosts = todayPostsSnapshot.docs.length;

      // حساب إجمالي التعليقات
      final totalCommentsSnapshot =
          await _firestore.collection(_commentsCollection).get();
      final totalComments = totalCommentsSnapshot.docs.length;

      // حساب تعليقات اليوم
      final todayCommentsSnapshot =
          await _firestore
              .collection(_commentsCollection)
              .where(
                'timestamp',
                isGreaterThanOrEqualTo: Timestamp.fromDate(today),
              )
              .get();
      final todayComments = todayCommentsSnapshot.docs.length;

      // حساب المستخدمين النشطين (الذين نشروا أو علقوا في آخر 7 أيام)
      final weekAgo = now.subtract(const Duration(days: 7));
      final activeUsersSet = <String>{};

      final recentPostsSnapshot =
          await _firestore
              .collection(_postsCollection)
              .where(
                'timestamp',
                isGreaterThanOrEqualTo: Timestamp.fromDate(weekAgo),
              )
              .get();

      for (final doc in recentPostsSnapshot.docs) {
        final data = doc.data();
        activeUsersSet.add(data['authorId'] ?? '');
      }

      final recentCommentsSnapshot =
          await _firestore
              .collection(_commentsCollection)
              .where(
                'timestamp',
                isGreaterThanOrEqualTo: Timestamp.fromDate(weekAgo),
              )
              .get();

      for (final doc in recentCommentsSnapshot.docs) {
        final data = doc.data();
        activeUsersSet.add(data['authorId'] ?? '');
      }

      final activeUsers = activeUsersSet.length;

      // حساب إجمالي المستخدمين المسجلين
      final totalUsersSnapshot =
          await _firestore.collection(_usersCollection).get();
      final totalUsers = totalUsersSnapshot.docs.length;

      final stats = CommunityStats(
        totalUsers: totalUsers,
        activeUsers: activeUsers,
        totalPosts: totalPosts,
        todayPosts: todayPosts,
        totalComments: totalComments,
        todayComments: todayComments,
        lastUpdated: now,
      );

      await _firestore
          .collection(_statsCollection)
          .doc('global')
          .set(stats.toMap());

      AppLogger.info(
        'تم تحديث الإحصائيات - المنشورات: $totalPosts، اليوم: $todayPosts، المستخدمون: $activeUsers',
        'CommunityService',
      );
    } catch (e) {
      AppLogger.error('خطأ في تحديث الإحصائيات', 'CommunityService', e);
    }
  }

  /// تهيئة الإحصائيات
  static Future<void> initializeStats() async {
    try {
      await _updateStats();
      AppLogger.info('تم تهيئة إحصائيات المجتمع', 'CommunityService');
    } catch (e) {
      AppLogger.error('خطأ في تهيئة الإحصائيات', 'CommunityService', e);
    }
  }
}

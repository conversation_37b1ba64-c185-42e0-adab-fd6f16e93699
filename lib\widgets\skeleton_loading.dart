import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';

/// ويدجت Skeleton Loading مع تأثيرات متحركة جميلة
class SkeletonLoading extends StatefulWidget {
  final double? width;
  final double? height;
  final BorderRadius? borderRadius;
  final EdgeInsets? margin;

  const SkeletonLoading({
    super.key,
    this.width,
    this.height,
    this.borderRadius,
    this.margin,
  });

  @override
  State<SkeletonLoading> createState() => _SkeletonLoadingState();
}

class _SkeletonLoadingState extends State<SkeletonLoading>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(begin: -1.0, end: 2.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Container(
          width: widget.width,
          height: widget.height,
          margin: widget.margin,
          decoration: BoxDecoration(
            borderRadius: widget.borderRadius ?? BorderRadius.circular(8),
            color: themeProvider.isDarkMode
                ? const Color(0xFF2D3748)
                : const Color(0xFFE2E8F0),
          ),
          child: AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return Container(
                decoration: BoxDecoration(
                  borderRadius: widget.borderRadius ?? BorderRadius.circular(8),
                  gradient: LinearGradient(
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                    stops: [
                      (_animation.value - 1).clamp(0.0, 1.0),
                      _animation.value.clamp(0.0, 1.0),
                      (_animation.value + 1).clamp(0.0, 1.0),
                    ],
                    colors: themeProvider.isDarkMode
                        ? [
                            const Color(0xFF2D3748),
                            const Color(0xFF4A5568),
                            const Color(0xFF2D3748),
                          ]
                        : [
                            const Color(0xFFE2E8F0),
                            const Color(0xFFF7FAFC),
                            const Color(0xFFE2E8F0),
                          ],
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }
}

/// Skeleton للمنشورات
class PostSkeleton extends StatelessWidget {
  const PostSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: themeProvider.isDarkMode
                ? const Color(0xFF1E293B)
                : Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: themeProvider.isDarkMode
                    ? Colors.black.withValues(alpha: 0.2)
                    : Colors.black.withValues(alpha: 0.03),
                blurRadius: 6,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header - صورة المستخدم والاسم
              Row(
                children: [
                  const SkeletonLoading(
                    width: 40,
                    height: 40,
                    borderRadius: BorderRadius.all(Radius.circular(20)),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SkeletonLoading(
                          width: 120,
                          height: 16,
                          borderRadius: BorderRadius.all(Radius.circular(8)),
                        ),
                        const SizedBox(height: 4),
                        SkeletonLoading(
                          width: 80,
                          height: 12,
                          borderRadius: const BorderRadius.all(Radius.circular(6)),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              
              // Content - النص
              const SkeletonLoading(
                width: double.infinity,
                height: 16,
                borderRadius: BorderRadius.all(Radius.circular(8)),
              ),
              const SizedBox(height: 8),
              const SkeletonLoading(
                width: double.infinity,
                height: 16,
                borderRadius: BorderRadius.all(Radius.circular(8)),
              ),
              const SizedBox(height: 8),
              const SkeletonLoading(
                width: 200,
                height: 16,
                borderRadius: BorderRadius.all(Radius.circular(8)),
              ),
              const SizedBox(height: 16),
              
              // Actions - الأزرار
              Row(
                children: [
                  const SkeletonLoading(
                    width: 60,
                    height: 32,
                    borderRadius: BorderRadius.all(Radius.circular(16)),
                  ),
                  const SizedBox(width: 12),
                  const SkeletonLoading(
                    width: 60,
                    height: 32,
                    borderRadius: BorderRadius.all(Radius.circular(16)),
                  ),
                  const SizedBox(width: 12),
                  const SkeletonLoading(
                    width: 60,
                    height: 32,
                    borderRadius: BorderRadius.all(Radius.circular(16)),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}

/// Skeleton للتعليقات
class CommentSkeleton extends StatelessWidget {
  const CommentSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      padding: const EdgeInsets.all(12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SkeletonLoading(
            width: 32,
            height: 32,
            borderRadius: BorderRadius.all(Radius.circular(16)),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SkeletonLoading(
                  width: 100,
                  height: 14,
                  borderRadius: BorderRadius.all(Radius.circular(7)),
                ),
                const SizedBox(height: 6),
                const SkeletonLoading(
                  width: double.infinity,
                  height: 14,
                  borderRadius: BorderRadius.all(Radius.circular(7)),
                ),
                const SizedBox(height: 4),
                const SkeletonLoading(
                  width: 150,
                  height: 14,
                  borderRadius: BorderRadius.all(Radius.circular(7)),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    const SkeletonLoading(
                      width: 40,
                      height: 24,
                      borderRadius: BorderRadius.all(Radius.circular(12)),
                    ),
                    const SizedBox(width: 8),
                    const SkeletonLoading(
                      width: 40,
                      height: 24,
                      borderRadius: BorderRadius.all(Radius.circular(12)),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// Skeleton لقائمة ملفات PDF
class PdfListSkeleton extends StatelessWidget {
  const PdfListSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: themeProvider.isDarkMode
                ? const Color(0xFF1E293B)
                : Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: themeProvider.isDarkMode
                    ? Colors.black.withValues(alpha: 0.2)
                    : Colors.black.withValues(alpha: 0.05),
                blurRadius: 4,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Row(
            children: [
              const SkeletonLoading(
                width: 48,
                height: 48,
                borderRadius: BorderRadius.all(Radius.circular(8)),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SkeletonLoading(
                      width: double.infinity,
                      height: 16,
                      borderRadius: BorderRadius.all(Radius.circular(8)),
                    ),
                    const SizedBox(height: 8),
                    const SkeletonLoading(
                      width: 120,
                      height: 12,
                      borderRadius: BorderRadius.all(Radius.circular(6)),
                    ),
                    const SizedBox(height: 4),
                    const SkeletonLoading(
                      width: 80,
                      height: 12,
                      borderRadius: BorderRadius.all(Radius.circular(6)),
                    ),
                  ],
                ),
              ),
              const SkeletonLoading(
                width: 32,
                height: 32,
                borderRadius: BorderRadius.all(Radius.circular(16)),
              ),
            ],
          ),
        );
      },
    );
  }
}

/// Skeleton للرسائل في الدردشة
class MessageSkeleton extends StatelessWidget {
  final bool isMe;
  
  const MessageSkeleton({super.key, this.isMe = false});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Row(
        mainAxisAlignment: isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        children: [
          if (!isMe) ...[
            const SkeletonLoading(
              width: 32,
              height: 32,
              borderRadius: BorderRadius.all(Radius.circular(16)),
            ),
            const SizedBox(width: 8),
          ],
          Container(
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.7,
            ),
            child: Column(
              crossAxisAlignment: isMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
              children: [
                SkeletonLoading(
                  width: 150,
                  height: 40,
                  borderRadius: BorderRadius.circular(20),
                ),
                const SizedBox(height: 4),
                const SkeletonLoading(
                  width: 60,
                  height: 12,
                  borderRadius: BorderRadius.all(Radius.circular(6)),
                ),
              ],
            ),
          ),
          if (isMe) ...[
            const SizedBox(width: 8),
            const SkeletonLoading(
              width: 32,
              height: 32,
              borderRadius: BorderRadius.all(Radius.circular(16)),
            ),
          ],
        ],
      ),
    );
  }
}

import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/app_logger.dart';
import 'community_service.dart';

/// خدمة تنظيف المنشورات القديمة
class PostCleanupService {
  static Timer? _cleanupTimer;
  static const String _lastCleanupKey = 'last_posts_cleanup';
  static const Duration _cleanupInterval = Duration(days: 1); // يوميًا
  static const Duration _cleanupFrequency = Duration(days: 7); // كل أسبوع

  /// بدء خدمة التنظيف التلقائي
  static Future<void> startCleanupService() async {
    try {
      // التحقق من آخر مرة تم فيها التنظيف
      final shouldCleanup = await _shouldPerformCleanup();
      
      if (shouldCleanup) {
        await _performCleanup();
      }

      // جدولة التنظيف التلقائي
      _schedulePeriodicCleanup();
      
      AppLogger.info('تم بدء خدمة تنظيف المنشورات', 'PostCleanupService');
    } catch (e) {
      AppLogger.error('خطأ في بدء خدمة التنظيف', 'PostCleanupService', e);
    }
  }

  /// إيقاف خدمة التنظيف
  static void stopCleanupService() {
    _cleanupTimer?.cancel();
    _cleanupTimer = null;
    AppLogger.info('تم إيقاف خدمة تنظيف المنشورات', 'PostCleanupService');
  }

  /// جدولة التنظيف الدوري
  static void _schedulePeriodicCleanup() {
    _cleanupTimer?.cancel();
    
    _cleanupTimer = Timer.periodic(_cleanupInterval, (timer) async {
      final shouldCleanup = await _shouldPerformCleanup();
      if (shouldCleanup) {
        await _performCleanup();
      }
    });
  }

  /// التحقق من ضرورة إجراء التنظيف
  static Future<bool> _shouldPerformCleanup() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastCleanupString = prefs.getString(_lastCleanupKey);
      
      if (lastCleanupString == null) {
        return true; // لم يتم التنظيف من قبل
      }
      
      final lastCleanup = DateTime.parse(lastCleanupString);
      final now = DateTime.now();
      
      return now.difference(lastCleanup) >= _cleanupFrequency;
    } catch (e) {
      AppLogger.error('خطأ في التحقق من ضرورة التنظيف', 'PostCleanupService', e);
      return false;
    }
  }

  /// تنفيذ عملية التنظيف
  static Future<void> _performCleanup() async {
    try {
      AppLogger.info('بدء عملية تنظيف المنشورات القديمة', 'PostCleanupService');
      
      final deletedCount = await CommunityService.deleteOldPosts();
      
      if (deletedCount > 0) {
        AppLogger.success(
          'تم حذف $deletedCount منشور قديم بنجاح',
          'PostCleanupService',
        );
      } else {
        AppLogger.info('لا توجد منشورات قديمة للحذف', 'PostCleanupService');
      }
      
      // تحديث وقت آخر تنظيف
      await _updateLastCleanupTime();
      
    } catch (e) {
      AppLogger.error('خطأ في تنفيذ عملية التنظيف', 'PostCleanupService', e);
    }
  }

  /// تحديث وقت آخر تنظيف
  static Future<void> _updateLastCleanupTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastCleanupKey, DateTime.now().toIso8601String());
    } catch (e) {
      AppLogger.error('خطأ في تحديث وقت التنظيف', 'PostCleanupService', e);
    }
  }

  /// تنظيف يدوي فوري
  static Future<int> performManualCleanup() async {
    try {
      AppLogger.info('بدء التنظيف اليدوي للمنشورات', 'PostCleanupService');
      
      final deletedCount = await CommunityService.deleteOldPosts();
      await _updateLastCleanupTime();
      
      AppLogger.success(
        'تم التنظيف اليدوي - حذف $deletedCount منشور',
        'PostCleanupService',
      );
      
      return deletedCount;
    } catch (e) {
      AppLogger.error('خطأ في التنظيف اليدوي', 'PostCleanupService', e);
      return 0;
    }
  }

  /// الحصول على إحصائيات التنظيف
  static Future<Map<String, dynamic>> getCleanupStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastCleanupString = prefs.getString(_lastCleanupKey);
      
      DateTime? lastCleanup;
      if (lastCleanupString != null) {
        lastCleanup = DateTime.parse(lastCleanupString);
      }
      
      final nextCleanup = lastCleanup?.add(_cleanupFrequency);
      final isServiceRunning = _cleanupTimer?.isActive ?? false;
      
      return {
        'lastCleanup': lastCleanup,
        'nextCleanup': nextCleanup,
        'isServiceRunning': isServiceRunning,
        'cleanupFrequencyDays': _cleanupFrequency.inDays,
      };
    } catch (e) {
      AppLogger.error('خطأ في جلب إحصائيات التنظيف', 'PostCleanupService', e);
      return {};
    }
  }
}

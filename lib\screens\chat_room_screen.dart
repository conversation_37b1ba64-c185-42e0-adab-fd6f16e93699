import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'dart:ui';
import '../theme/app_theme.dart';
import '../providers/theme_provider.dart';

import '../models/chat_model.dart';
import '../services/chat_service.dart';
import 'dart:async';
import '../widgets/skeleton_loading.dart';
import '../services/smart_cache_service.dart';

class ChatRoomScreen extends StatefulWidget {
  final ChatRoomModel chatRoom;

  const ChatRoomScreen({super.key, required this.chatRoom});

  @override
  State<ChatRoomScreen> createState() => _ChatRoomScreenState();
}

class _ChatRoomScreenState extends State<ChatRoomScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final FocusNode _messageFocusNode = FocusNode();

  bool _isUserMember = false;
  bool _isJoining = false;
  bool _isTyping = false;
  Timer? _typingTimer;
  Timer? _cleanupTimer;

  List<MessageModel> _messages = [];
  List<TypingIndicator> _typingIndicators = [];
  List<ChatUser> _onlineUsers = [];

  // متغيرات التحميل والتصفح
  bool _isLoadingMessages = true; // يبدأ بـ true لإظهار التحميل
  bool _hasMoreMessages = true;
  bool _isLoadingMore = false;

  // حد النص
  static const int _maxMessageLength = 3000;

  // متغيرات الرد
  MessageModel? _replyToMessage;
  bool _isReplying = false;

  // متغيرات تأثير السحب
  double _swipeOffset = 0.0;
  String? _swipingMessageId;

  // متغيرات الضغط المطول
  OverlayEntry? _overlayEntry;

  @override
  void initState() {
    super.initState();
    _resetChatState();
    _initializeChat();
    _setupTypingListener();
    _setupScrollListener();
    _startCleanupTimer();
  }

  void _resetChatState() {
    // إعادة تعيين حالة الدردشة لضمان البدء بـ 10 رسائل فقط
    _messages.clear();
    _isLoadingMessages = true;
    _hasMoreMessages = true;
    _isLoadingMore = false;
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      // تحميل المزيد من الرسائل عند الوصول للأعلى
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent - 200) {
        if (!_isLoadingMore && _hasMoreMessages && _messages.isNotEmpty) {
          _loadMoreMessages();
        }
      }
    });
  }

  Future<void> _loadMoreMessages() async {
    if (_isLoadingMore || !_hasMoreMessages || _messages.isEmpty) return;

    if (mounted) {
      setState(() {
        _isLoadingMore = true;
      });
    }

    try {
      // استخدام الطريقة الأصلية للتحميل التدريجي
      final oldestMessage = _messages.last;
      final moreMessages = await ChatService.loadMoreMessages(
        widget.chatRoom.id,
        oldestMessage.timestamp!,
        limit: 10,
      );

      if (moreMessages.isNotEmpty) {
        if (mounted) {
          setState(() {
            // إضافة الرسائل الأقدم في النهاية
            _messages.addAll(moreMessages);
            _hasMoreMessages = moreMessages.length == 10;
            _isLoadingMore = false;
          });
        }

        // تم تحميل ${moreMessages.length} رسائل إضافية
        // إجمالي الرسائل الآن: ${_messages.length}
      } else {
        if (mounted) {
          setState(() {
            _hasMoreMessages = false;
            _isLoadingMore = false;
          });
        }
        // لا توجد رسائل أقدم
      }
    } catch (e) {
      // خطأ في تحميل الرسائل: $e
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    _messageFocusNode.dispose();
    _typingTimer?.cancel();
    _cleanupTimer?.cancel();
    _hideMessageOptions();
    super.dispose();
  }

  /// تحديث الرسائل عند السحب للأسفل
  Future<void> _refreshMessages() async {
    try {
      // مسح Cache للحصول على بيانات جديدة
      await SmartCacheService.clear();

      // إعادة تعيين حالة الدردشة
      _resetChatState();

      // إعادة تهيئة الاستماع للرسائل
      if (_isUserMember || widget.chatRoom.isGeneral) {
        _listenToMessages();
      }

      // انتظار قصير للسماح للبيانات بالتحديث
      await Future.delayed(const Duration(milliseconds: 500));
    } catch (e) {
      if (kDebugMode) print('خطأ في تحديث الرسائل: $e');
    }
  }

  void _initializeChat() async {
    // التحقق من عضوية المستخدم
    if (!widget.chatRoom.isGeneral) {
      _isUserMember = await ChatService.isUserMember(widget.chatRoom.id);
      if (mounted) {
        setState(() {});
      }
    } else {
      _isUserMember = true;
    }

    // تحديث حالة الحضور
    await ChatService.updatePresence(true);

    // الاستماع للرسائل إذا كان المستخدم عضواً
    if (_isUserMember || widget.chatRoom.isGeneral) {
      _listenToMessages();
      _listenToTypingIndicators();
      _listenToOnlineUsers();
    }
  }

  void _listenToMessages() {
    // استخدام الطريقة الموثوقة مع تحديد عدد الرسائل
    ChatService.getMessagesStream(widget.chatRoom.id).listen((messages) {
      if (mounted) {
        setState(() {
          // عرض آخر 10 رسائل فقط في البداية
          if (messages.length > 10) {
            _messages = messages.take(10).toList();
            _hasMoreMessages = true;
          } else {
            _messages = messages;
            _hasMoreMessages = false;
          }
          _isLoadingMessages = false;
        });
      }
      _scrollToBottom();
    });
  }

  void _listenToTypingIndicators() {
    ChatService.getTypingIndicatorsStream(widget.chatRoom.id).listen((
      indicators,
    ) {
      if (mounted) {
        setState(() {
          _typingIndicators = indicators;
        });
      }
    });
  }

  void _listenToOnlineUsers() {
    ChatService.getOnlineUsersStream().listen((users) {
      if (mounted) {
        setState(() {
          _onlineUsers = users;
        });
      }
    });
  }

  void _setupTypingListener() {
    _messageController.addListener(() {
      if (_messageController.text.isNotEmpty && !_isTyping) {
        _isTyping = true;
        ChatService.sendTypingIndicator(widget.chatRoom.id, true);
      }

      // إعادة تعيين مؤقت الكتابة
      _typingTimer?.cancel();
      _typingTimer = Timer(const Duration(seconds: 2), () {
        if (_isTyping) {
          _isTyping = false;
          ChatService.sendTypingIndicator(widget.chatRoom.id, false);
        }
      });
    });
  }

  void _startCleanupTimer() {
    // تنظيف مؤشرات الكتابة المنتهية الصلاحية كل 10 ثوان
    _cleanupTimer = Timer.periodic(const Duration(seconds: 10), (_) {
      ChatService.cleanupExpiredTypingIndicators();
    });
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        // مع reverse: true، نحتاج للتمرير إلى 0.0 للوصول للأسفل
        _scrollController.animateTo(
          0.0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  Future<void> _joinChatRoom() async {
    if (mounted) {
      setState(() {
        _isJoining = true;
      });
    }

    final success = await ChatService.joinChatRoom(widget.chatRoom.id);

    if (success) {
      if (mounted) {
        setState(() {
          _isUserMember = true;
          _isJoining = false;
        });
      }

      // بدء الاستماع للرسائل بعد الانضمام
      _listenToMessages();
      _listenToTypingIndicators();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم الانضمام إلى ${widget.chatRoom.name} بنجاح',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } else {
      if (mounted) {
        setState(() {
          _isJoining = false;
        });
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'فشل في الانضمام إلى الغرفة',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _leaveChatRoom() async {
    // للمحادثة العامة - مجرد الخروج من الصفحة
    if (widget.chatRoom.isGeneral) {
      Navigator.pop(context);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم الخروج من ${widget.chatRoom.name}',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.orange,
          ),
        );
      }
      return;
    }

    // للمحادثات الخاصة - عرض تأكيد ومغادرة فعلية
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'مغادرة الغرفة',
              style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
            ),
            content: Text(
              'هل أنت متأكد من مغادرة ${widget.chatRoom.name}؟',
              style: GoogleFonts.cairo(),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: Text('إلغاء', style: GoogleFonts.cairo()),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                child: Text(
                  'مغادرة',
                  style: GoogleFonts.cairo(color: Colors.red),
                ),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      final success = await ChatService.leaveChatRoom(widget.chatRoom.id);

      if (success && mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم مغادرة ${widget.chatRoom.name}',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.orange,
          ),
        );
      }
    }
  }

  Future<void> _sendMessage() async {
    final message = _messageController.text.trim();
    if (message.isEmpty) return;

    // إيقاف مؤشر الكتابة
    if (_isTyping) {
      _isTyping = false;
      ChatService.sendTypingIndicator(widget.chatRoom.id, false);
    }

    _messageController.clear();

    bool success;

    // إرسال رسالة عادية أو رد (محسن للأداء)
    if (_isReplying && _replyToMessage != null) {
      // إرسال رد بتنسيق خاص
      final replyMessage =
          'REPLY_TO:${_replyToMessage!.id}|${_replyToMessage!.senderName}|${_replyToMessage!.message}|REPLY_MSG:$message';

      success = await ChatService.sendMessage(
        chatRoomId: widget.chatRoom.id,
        message: replyMessage,
      );

      // إلغاء حالة الرد فوراً
      _cancelReply();
    } else {
      // إرسال رسالة عادية
      success = await ChatService.sendMessage(
        chatRoomId: widget.chatRoom.id,
        message: message,
      );
    }

    if (!success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('فشل في إرسال الرسالة', style: GoogleFonts.cairo()),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// بدء الرد على رسالة (محسن للأداء)
  void _startReply(MessageModel message) {
    // تجنب الرد المتكرر على نفس الرسالة
    if (_isReplying && _replyToMessage?.id == message.id) {
      return;
    }

    if (mounted) {
      setState(() {
        _replyToMessage = message;
        _isReplying = true;
      });
    }

    // التركيز على حقل الإدخال بدون تأخير
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _messageFocusNode.requestFocus();
    });
  }

  /// عرض خيارات الرسالة مع تأثير البلور
  void _showMessageOptions(MessageModel message, Offset position) {
    _hideMessageOptions(); // إخفاء أي خيارات سابقة

    _overlayEntry = OverlayEntry(
      builder: (context) => _buildMessageOptionsOverlay(message, position),
    );

    Overlay.of(context).insert(_overlayEntry!);
    HapticFeedback.mediumImpact();
  }

  /// إخفاء خيارات الرسالة
  void _hideMessageOptions() {
    if (_overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
    }
  }

  /// حذف الرسالة
  Future<void> _deleteMessage(MessageModel message) async {
    try {
      await ChatService.deleteMessage(widget.chatRoom.id, message.id);
      _hideMessageOptions();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم حذف الرسالة', style: GoogleFonts.cairo()),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }
    } catch (e) {
      _hideMessageOptions();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في حذف الرسالة', style: GoogleFonts.cairo()),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }
    }
  }

  /// إلغاء الرد
  void _cancelReply() {
    if (mounted) {
      setState(() {
        _replyToMessage = null;
        _isReplying = false;
      });
    }
  }

  /// تحليل الرسالة للتحقق من كونها رد
  Map<String, String>? _parseReplyMessage(String message) {
    if (message.startsWith('REPLY_TO:')) {
      try {
        final parts = message.split('|');
        if (parts.length >= 4) {
          final replyToId = parts[0].substring(9); // إزالة 'REPLY_TO:'
          final replyToSender = parts[1];
          final replyToMessage = parts[2];
          final replyMsg = parts[3].substring(10); // إزالة 'REPLY_MSG:'

          return {
            'replyToId': replyToId,
            'replyToSender': replyToSender,
            'replyToMessage': replyToMessage,
            'replyMsg': replyMsg,
          };
        }
      } catch (e) {
        // في حالة خطأ في التحليل، إرجاع null
      }
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          backgroundColor:
              themeProvider.isDarkMode
                  ? const Color(0xFF0F172A)
                  : const Color(0xFFF8FAFC),
          appBar: _buildAppBar(themeProvider),
          body: Column(
            children: [
              // محتوى المحادثة
              Expanded(
                child:
                    _isUserMember || widget.chatRoom.isGeneral
                        ? _buildChatContent(themeProvider)
                        : _buildJoinPrompt(themeProvider),
              ),

              // مؤشرات الكتابة
              if (_typingIndicators.isNotEmpty)
                _buildTypingIndicators(themeProvider),

              // معاينة الرد
              if (_isReplying && _replyToMessage != null)
                _buildReplyPreview(themeProvider),

              // شريط إدخال الرسالة
              if (_isUserMember || widget.chatRoom.isGeneral)
                _buildMessageInput(themeProvider),
            ],
          ),
        );
      },
    );
  }

  PreferredSizeWidget _buildAppBar(ThemeProvider themeProvider) {
    return AppBar(
      backgroundColor:
          themeProvider.isDarkMode ? const Color(0xFF1E293B) : Colors.white,
      elevation: 1,
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back_ios_rounded,
          color: themeProvider.isDarkMode ? Colors.white : Colors.black87,
        ),
        onPressed: () => Navigator.pop(context),
      ),
      actions: [
        // زر الخروج للمجتمع العام
        if (widget.chatRoom.isGeneral)
          Container(
            margin: const EdgeInsets.only(right: 8),
            child: IconButton(
              icon: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.exit_to_app_rounded,
                  color: Colors.red.shade600,
                  size: 20,
                ),
              ),
              onPressed: () => _showExitConfirmation(themeProvider),
              tooltip: 'الخروج من المحادثة العامة',
            ),
          ),
        // زر المعلومات
        Container(
          margin: const EdgeInsets.only(right: 8),
          child: IconButton(
            icon: Icon(
              Icons.info_outline_rounded,
              color: themeProvider.isDarkMode ? Colors.white70 : Colors.black54,
            ),
            onPressed: () {
              // عرض معلومات المحادثة
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'معلومات المحادثة: ${widget.chatRoom.name}',
                    style: GoogleFonts.cairo(),
                  ),
                  backgroundColor: AppTheme.primaryColor,
                ),
              );
            },
            tooltip: 'معلومات المحادثة',
          ),
        ),
      ],
      title: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors:
                    widget.chatRoom.isGeneral
                        ? [const Color(0xFF667EEA), const Color(0xFF764BA2)]
                        : [const Color(0xFF4FACFE), const Color(0xFF00F2FE)],
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              widget.chatRoom.isGeneral
                  ? Icons.public_rounded
                  : Icons.school_rounded,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.chatRoom.name,
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color:
                        themeProvider.isDarkMode
                            ? Colors.white
                            : Colors.black87,
                  ),
                ),
                if (_onlineUsers.isNotEmpty)
                  Text(
                    '${_onlineUsers.length} متصل',
                    style: GoogleFonts.cairo(fontSize: 12, color: Colors.green),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChatContent(ThemeProvider themeProvider) {
    // حالة التحميل الأولي
    if (_isLoadingMessages) {
      return ListView.builder(
        reverse: true,
        padding: const EdgeInsets.all(16),
        itemCount: 8, // عرض 8 skeleton messages
        itemBuilder:
            (context, index) => MessageSkeleton(
              isMe: index % 3 == 0, // تنويع الرسائل بين المرسل والمستقبل
            ),
      );
    }

    // حالة عدم وجود رسائل
    if (_messages.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.chat_bubble_outline,
                size: 48,
                color: AppTheme.primaryColor,
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'لا توجد رسائل بعد',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color:
                    themeProvider.isDarkMode ? Colors.white : Colors.grey[800],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'ابدأ المحادثة بإرسال أول رسالة',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color:
                    themeProvider.isDarkMode
                        ? Colors.white60
                        : Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    // عرض الرسائل مع مؤشر التحميل
    return Column(
      children: [
        // مؤشر تحميل المزيد في الأعلى
        if (_isLoadingMore)
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: AppTheme.primaryColor,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'جاري تحميل رسائل أقدم...',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color:
                        themeProvider.isDarkMode
                            ? Colors.white60
                            : Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),

        // قائمة الرسائل
        Expanded(
          child: RefreshIndicator(
            onRefresh: _refreshMessages,
            color: AppTheme.primaryColor,
            backgroundColor:
                themeProvider.isDarkMode
                    ? const Color(0xFF1E293B)
                    : Colors.white,
            child: ListView.builder(
              controller: _scrollController,
              reverse: true, // الأحدث في الأسفل
              padding: const EdgeInsets.all(16),
              itemCount: _messages.length,
              itemBuilder: (context, index) {
                final message = _messages[index];
                final isMe =
                    message.senderId == FirebaseAuth.instance.currentUser?.uid;
                final showSender =
                    index == _messages.length - 1 ||
                    _messages[index + 1].senderId != message.senderId;

                return _buildMessageBubble(
                  message,
                  isMe,
                  showSender,
                  themeProvider,
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMessageBubble(
    MessageModel message,
    bool isMe,
    bool showSender,
    ThemeProvider themeProvider,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment:
            isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (!isMe) ...[
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                gradient: LinearGradient(
                  colors: [
                    AppTheme.primaryColor.withValues(alpha: 0.8),
                    AppTheme.secondaryColor.withValues(alpha: 0.8),
                  ],
                ),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.primaryColor.withValues(alpha: 0.2),
                    blurRadius: 6,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: CircleAvatar(
                radius: 18,
                backgroundColor: Colors.transparent,
                child: Text(
                  message.senderName.isNotEmpty
                      ? message.senderName[0].toUpperCase()
                      : 'م',
                  style: GoogleFonts.cairo(
                    color: Colors.white,
                    fontSize: 13,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
          ],
          Flexible(
            child: Column(
              crossAxisAlignment:
                  isMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
              children: [
                if (showSender && !isMe)
                  Container(
                    margin: const EdgeInsets.only(
                      bottom: 6,
                      left: 16,
                      right: 16,
                    ),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      message.senderName,
                      style: GoogleFonts.cairo(
                        fontSize: 11,
                        color: AppTheme.primaryColor,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ),
                // إضافة GestureDetector للنقر والسحب للرد والضغط المطول
                GestureDetector(
                  // النقر السريع للرد
                  onTap: () {
                    _startReply(message);
                    HapticFeedback.lightImpact();
                  },

                  // الضغط المطول لعرض خيارات الرسالة
                  onLongPressStart: (details) {
                    final RenderBox renderBox =
                        context.findRenderObject() as RenderBox;
                    final position = renderBox.globalToLocal(
                      details.globalPosition,
                    );
                    _showMessageOptions(message, position);
                  },

                  // السحب للرد مع تأثيرات بصرية
                  onPanStart: (details) {
                    if (mounted) {
                      setState(() {
                        _swipingMessageId = message.id;
                        _swipeOffset = 0.0;
                      });
                    }
                  },

                  onPanUpdate: (details) {
                    if (mounted) {
                      setState(() {
                        _swipeOffset += details.delta.dx;
                        // حد أقصى للسحب
                        _swipeOffset = _swipeOffset.clamp(0.0, 100.0);
                      });
                    }

                    // تفعيل الرد عند السحب 50 بكسل
                    if (_swipeOffset > 50 && _swipingMessageId == message.id) {
                      _startReply(message);
                      HapticFeedback.mediumImpact();

                      // إعادة تعيين السحب
                      if (mounted) {
                        setState(() {
                          _swipeOffset = 0.0;
                          _swipingMessageId = null;
                        });
                      }
                    }
                  },

                  onPanEnd: (details) {
                    // إعادة تعيين السحب
                    if (mounted) {
                      setState(() {
                        _swipeOffset = 0.0;
                        _swipingMessageId = null;
                      });
                    }
                  },
                  child: Stack(
                    children: [
                      // تأثير السحب الخلفي
                      if (_swipingMessageId == message.id && _swipeOffset > 0)
                        Positioned.fill(
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                                colors: [
                                  AppTheme.primaryColor.withValues(alpha: 0.1),
                                  AppTheme.primaryColor.withValues(alpha: 0.05),
                                  Colors.transparent,
                                ],
                                stops: [0.0, 0.3, 1.0],
                              ),
                            ),
                          ),
                        ),

                      // أيقونة الرد عند السحب
                      if (_swipingMessageId == message.id && _swipeOffset > 20)
                        Positioned(
                          left: 16,
                          top: 0,
                          bottom: 0,
                          child: Center(
                            child: AnimatedContainer(
                              duration: const Duration(milliseconds: 200),
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: AppTheme.primaryColor.withValues(
                                  alpha: 0.8,
                                ),
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.reply_rounded,
                                color: Colors.white,
                                size: _swipeOffset > 50 ? 24 : 18,
                              ),
                            ),
                          ),
                        ),

                      // الحاوية الرئيسية مع تأثير السحب
                      Transform.translate(
                        offset: Offset(
                          _swipingMessageId == message.id ? _swipeOffset : 0.0,
                          0.0,
                        ),
                        child: Container(
                          constraints: BoxConstraints(
                            maxWidth: MediaQuery.of(context).size.width * 0.75,
                          ),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 18,
                            vertical: 14,
                          ),
                          decoration: BoxDecoration(
                            gradient:
                                isMe
                                    ? LinearGradient(
                                      colors: [
                                        AppTheme.primaryColor,
                                        AppTheme.secondaryColor,
                                      ],
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                    )
                                    : null,
                            color:
                                isMe
                                    ? null
                                    : themeProvider.isDarkMode
                                    ? const Color(0xFF374151)
                                    : const Color(0xFFF1F5F9),
                            borderRadius: BorderRadius.only(
                              topLeft: const Radius.circular(20),
                              topRight: const Radius.circular(20),
                              bottomLeft: Radius.circular(isMe ? 20 : 6),
                              bottomRight: Radius.circular(isMe ? 6 : 20),
                            ),
                            boxShadow: [
                              BoxShadow(
                                color:
                                    isMe
                                        ? AppTheme.primaryColor.withValues(
                                          alpha: 0.3,
                                        )
                                        : themeProvider.isDarkMode
                                        ? Colors.black.withValues(alpha: 0.2)
                                        : Colors.grey.withValues(alpha: 0.15),
                                blurRadius: 8,
                                offset: const Offset(0, 3),
                              ),
                            ],
                          ),
                          child: _buildMessageContent(
                            message,
                            isMe,
                            themeProvider,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 6),
                Padding(
                  padding: EdgeInsets.only(
                    left: isMe ? 0 : 16,
                    right: isMe ? 16 : 0,
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color:
                              themeProvider.isDarkMode
                                  ? Colors.white.withValues(alpha: 0.1)
                                  : Colors.grey.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          _formatTime(message.timestamp),
                          style: GoogleFonts.cairo(
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                            color:
                                themeProvider.isDarkMode
                                    ? Colors.white60
                                    : Colors.grey[600],
                          ),
                        ),
                      ),
                      if (isMe) ...[
                        const SizedBox(width: 6),
                        Icon(
                          Icons.done_all_rounded,
                          size: 14,
                          color: Colors.blue.shade400,
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
          if (isMe) ...[
            const SizedBox(width: 12),
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                gradient: LinearGradient(
                  colors: [
                    AppTheme.secondaryColor.withValues(alpha: 0.8),
                    AppTheme.primaryColor.withValues(alpha: 0.8),
                  ],
                ),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.secondaryColor.withValues(alpha: 0.2),
                    blurRadius: 6,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: CircleAvatar(
                radius: 18,
                backgroundColor: Colors.transparent,
                child: Text(
                  message.senderName.isNotEmpty
                      ? message.senderName[0].toUpperCase()
                      : 'أ',
                  style: GoogleFonts.cairo(
                    color: Colors.white,
                    fontSize: 13,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildJoinPrompt(ThemeProvider themeProvider) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [AppTheme.primaryColor, AppTheme.secondaryColor],
                ),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Icon(
                Icons.group_add_rounded,
                color: Colors.white,
                size: 40,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'انضم إلى ${widget.chatRoom.name}',
              style: GoogleFonts.cairo(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: themeProvider.isDarkMode ? Colors.white : Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              widget.chatRoom.description,
              style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: _isJoining ? null : _joinChatRoom,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 16,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
              ),
              child:
                  _isJoining
                      ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                      : Text(
                        'انضمام',
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTypingIndicators(ThemeProvider themeProvider) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Row(
        children: [
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color:
                  themeProvider.isDarkMode
                      ? const Color(0xFF374151).withValues(alpha: 0.9)
                      : const Color(0xFFF1F5F9).withValues(alpha: 0.95),
              borderRadius: BorderRadius.circular(18),
              border: Border.all(
                color:
                    themeProvider.isDarkMode
                        ? Colors.white.withValues(alpha: 0.08)
                        : Colors.grey.withValues(alpha: 0.15),
                width: 0.5,
              ),
              boxShadow: [
                BoxShadow(
                  color:
                      themeProvider.isDarkMode
                          ? Colors.black.withValues(alpha: 0.15)
                          : Colors.grey.withValues(alpha: 0.08),
                  blurRadius: 6,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // أيقونة الكتابة المصغرة
                Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withValues(alpha: 0.12),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.edit_rounded,
                    size: 10,
                    color: AppTheme.primaryColor,
                  ),
                ),
                const SizedBox(width: 8),
                // النقاط المتحركة المصغرة
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: List.generate(3, (index) {
                    return AnimatedContainer(
                      duration: Duration(milliseconds: 600 + (index * 200)),
                      margin: EdgeInsets.only(right: index < 2 ? 3 : 0),
                      width: 5,
                      height: 5,
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor.withValues(alpha: 0.7),
                        shape: BoxShape.circle,
                      ),
                    );
                  }),
                ),
                const SizedBox(width: 8),
                // النص المصغر
                Text(
                  _typingIndicators.length == 1
                      ? '${_typingIndicators.first.userName} يكتب...'
                      : '${_typingIndicators.length} يكتبون...',
                  style: GoogleFonts.cairo(
                    fontSize: 11,
                    fontWeight: FontWeight.w500,
                    color:
                        themeProvider.isDarkMode
                            ? Colors.white60
                            : Colors.grey[600],
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء محتوى الرسالة (عادية أو رد)
  Widget _buildMessageContent(
    MessageModel message,
    bool isMe,
    ThemeProvider themeProvider,
  ) {
    final replyData = _parseReplyMessage(message.message);

    if (replyData != null) {
      // رسالة رد - تصميم خاص مثل الصورة
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // الرسالة المردود عليها (محسنة)
          GestureDetector(
            onTap: () {
              // لا تفعل شيئاً عند الضغط على الحاوية المردود عليها
            },
            child: Container(
              width: double.infinity, // عرض كامل
              margin: const EdgeInsets.only(bottom: 10),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color:
                    isMe
                        ? Colors.white.withValues(alpha: 0.15)
                        : AppTheme.primaryColor.withValues(alpha: 0.08),
                borderRadius: BorderRadius.circular(12),
                border: Border(
                  right: BorderSide(color: AppTheme.primaryColor, width: 4),
                ),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.primaryColor.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // اسم المرسل الأصلي
                  Text(
                    replyData['replyToSender']!,
                    style: GoogleFonts.cairo(
                      fontSize: 13,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  // الرسالة الأصلية
                  Text(
                    replyData['replyToMessage']!,
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      height: 1.3,
                      color:
                          isMe
                              ? Colors.white.withValues(alpha: 0.85)
                              : themeProvider.isDarkMode
                              ? Colors.white.withValues(alpha: 0.75)
                              : Colors.black.withValues(alpha: 0.65),
                    ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ),

          // الرسالة الجديدة
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              replyData['replyMsg']!,
              style: GoogleFonts.cairo(
                fontSize: 15,
                height: 1.4,
                fontWeight: FontWeight.w500,
                color:
                    isMe
                        ? Colors.white
                        : themeProvider.isDarkMode
                        ? Colors.white
                        : const Color(0xFF1E293B),
              ),
            ),
          ),
        ],
      );
    } else {
      // رسالة عادية
      return Text(
        message.message,
        style: GoogleFonts.cairo(
          fontSize: 15,
          height: 1.4,
          fontWeight: FontWeight.w500,
          color:
              isMe
                  ? Colors.white
                  : themeProvider.isDarkMode
                  ? Colors.white
                  : const Color(0xFF1E293B),
        ),
      );
    }
  }

  /// بناء معاينة الرد
  Widget _buildReplyPreview(ThemeProvider themeProvider) {
    return Container(
      margin: const EdgeInsets.fromLTRB(12, 0, 12, 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color:
            themeProvider.isDarkMode
                ? const Color(0xFF374151)
                : const Color(0xFFF1F5F9),
        borderRadius: BorderRadius.circular(12),
        border: Border(
          right: BorderSide(color: AppTheme.primaryColor, width: 4),
        ),
      ),
      child: Row(
        children: [
          // أيقونة الرد
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.reply_rounded,
              color: AppTheme.primaryColor,
              size: 16,
            ),
          ),

          const SizedBox(width: 12),

          // محتوى الرد
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'الرد على ${_replyToMessage!.senderName}',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.primaryColor,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  _replyToMessage!.message.length > 30
                      ? '${_replyToMessage!.message.substring(0, 30)}...'
                      : _replyToMessage!.message,
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color:
                        themeProvider.isDarkMode
                            ? Colors.white.withValues(alpha: 0.7)
                            : Colors.black.withValues(alpha: 0.6),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),

          // زر الإلغاء
          GestureDetector(
            onTap: _cancelReply,
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.close_rounded,
                color: Colors.red,
                size: 16,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageInput(ThemeProvider themeProvider) {
    return Container(
      margin: const EdgeInsets.all(12),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            themeProvider.isDarkMode
                ? const Color(0xFF1E293B).withValues(alpha: 0.95)
                : Colors.white.withValues(alpha: 0.95),
            themeProvider.isDarkMode
                ? const Color(0xFF334155).withValues(alpha: 0.9)
                : const Color(0xFFF8FAFC).withValues(alpha: 0.9),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(28),
        border: Border.all(
          color:
              themeProvider.isDarkMode
                  ? Colors.white.withValues(alpha: 0.1)
                  : Colors.grey.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color:
                themeProvider.isDarkMode
                    ? Colors.black.withValues(alpha: 0.3)
                    : Colors.grey.withValues(alpha: 0.15),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        children: [
          // أيقونة الملفات المرفقة
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color:
                  themeProvider.isDarkMode
                      ? Colors.white.withValues(alpha: 0.1)
                      : Colors.grey.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              Icons.attach_file_rounded,
              size: 20,
              color:
                  themeProvider.isDarkMode ? Colors.white60 : Colors.grey[600],
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Container(
              constraints: const BoxConstraints(maxHeight: 120),
              decoration: BoxDecoration(
                color:
                    themeProvider.isDarkMode
                        ? const Color(0xFF374151).withValues(alpha: 0.6)
                        : const Color(0xFFF1F5F9).withValues(alpha: 0.8),
                borderRadius: BorderRadius.circular(22),
                border: Border.all(
                  color:
                      themeProvider.isDarkMode
                          ? Colors.white.withValues(alpha: 0.05)
                          : Colors.grey.withValues(alpha: 0.1),
                  width: 1,
                ),
              ),
              child: TextField(
                controller: _messageController,
                focusNode: _messageFocusNode,
                maxLength: _maxMessageLength,
                decoration: InputDecoration(
                  hintText:
                      _messageController.text.length > _maxMessageLength * 0.9
                          ? 'تجاوزت الحد المسموح (${_messageController.text.length}/$_maxMessageLength)'
                          : 'اكتب رسالة جميلة...',
                  hintStyle: GoogleFonts.cairo(
                    color:
                        _messageController.text.length > _maxMessageLength * 0.9
                            ? Colors.red
                            : themeProvider.isDarkMode
                            ? Colors.white.withValues(alpha: 0.4)
                            : Colors.grey[500],
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                  border: InputBorder.none,
                  counterText: '', // إخفاء العداد الافتراضي
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 18,
                    vertical: 14,
                  ),
                ),
                style: GoogleFonts.cairo(
                  color:
                      themeProvider.isDarkMode
                          ? Colors.white
                          : const Color(0xFF1E293B),
                  fontSize: 15,
                  fontWeight: FontWeight.w500,
                  height: 1.4,
                ),
                maxLines: null,
                textInputAction: TextInputAction.send,
                onSubmitted: (_) => _sendMessage(),
                onChanged: (text) {
                  if (mounted) {
                    setState(() {}); // لتحديث النص التوضيحي
                  }
                },
              ),
            ),
          ),
          const SizedBox(width: 12),
          // زر الإرسال المحسن
          AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [AppTheme.primaryColor, AppTheme.secondaryColor],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(24),
              boxShadow: [
                BoxShadow(
                  color: AppTheme.primaryColor.withValues(alpha: 0.4),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(24),
                onTap:
                    _messageController.text.trim().isNotEmpty
                        ? _sendMessage
                        : null,
                child: Container(
                  padding: const EdgeInsets.all(12),
                  child: AnimatedSwitcher(
                    duration: const Duration(milliseconds: 200),
                    child:
                        _messageController.text.trim().isNotEmpty
                            ? const Icon(
                              Icons.send_rounded,
                              color: Colors.white,
                              size: 22,
                            )
                            : Icon(
                              Icons.mic_rounded,
                              color: Colors.white.withValues(alpha: 0.7),
                              size: 22,
                            ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatTime(DateTime? timestamp) {
    if (timestamp == null) return '';

    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${timestamp.day}/${timestamp.month}';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}س';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}د';
    } else {
      return 'الآن';
    }
  }

  // عرض مربع حوار تأكيد الخروج من المحادثة
  void _showExitConfirmation(ThemeProvider themeProvider) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor:
                themeProvider.isDarkMode
                    ? const Color(0xFF1E293B)
                    : Colors.white,
            title: Text(
              'الخروج من المحادثة',
              style: GoogleFonts.cairo(
                color: themeProvider.isDarkMode ? Colors.white : Colors.black87,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            content: Text(
              'هل أنت متأكد من رغبتك في الخروج من المحادثة العامة؟',
              style: GoogleFonts.cairo(
                color:
                    themeProvider.isDarkMode ? Colors.white70 : Colors.black54,
              ),
              textAlign: TextAlign.center,
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  'إلغاء',
                  style: GoogleFonts.cairo(color: Colors.grey),
                ),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  _leaveChatRoom();
                },
                child: Text(
                  'خروج',
                  style: GoogleFonts.cairo(
                    color: Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            elevation: 10,
          ),
    );
  }

  /// بناء overlay خيارات الرسالة مع تأثير البلور
  Widget _buildMessageOptionsOverlay(MessageModel message, Offset position) {
    final currentUser = FirebaseAuth.instance.currentUser;
    final isMyMessage = message.senderId == currentUser?.uid;

    return Material(
      color: Colors.transparent,
      child: GestureDetector(
        onTap: _hideMessageOptions,
        child: SizedBox(
          width: double.infinity,
          height: double.infinity,
          child: Stack(
            children: [
              // تأثير البلور على الخلفية
              BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
                child: Container(color: Colors.black.withValues(alpha: 0.3)),
              ),

              // خيارات الرسالة
              Positioned(
                left: position.dx - 100,
                top: position.dy - 80,
                child: Container(
                  width: 200,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.2),
                        blurRadius: 20,
                        offset: const Offset(0, 8),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // خيار الرد
                      _buildOptionItem(
                        icon: Icons.reply_rounded,
                        title: 'رد',
                        color: const Color(0xFF6366F1),
                        onTap: () {
                          _hideMessageOptions();
                          _startReply(message);
                        },
                      ),

                      // خيار الحذف (فقط لصاحب الرسالة)
                      if (isMyMessage) ...[
                        const Divider(height: 1),
                        _buildOptionItem(
                          icon: Icons.delete_rounded,
                          title: 'حذف للجميع',
                          color: const Color(0xFFEF4444),
                          onTap: () => _showDeleteConfirmation(message),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء عنصر خيار
  Widget _buildOptionItem({
    required IconData icon,
    required String title,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 18),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// عرض تأكيد الحذف
  void _showDeleteConfirmation(MessageModel message) {
    _hideMessageOptions();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            title: Text(
              'حذف الرسالة',
              style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
            ),
            content: Text(
              'هل أنت متأكد من حذف هذه الرسالة؟ سيتم حذفها للجميع ولا يمكن التراجع عن هذا الإجراء.',
              style: GoogleFonts.cairo(),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  'إلغاء',
                  style: GoogleFonts.cairo(color: Colors.grey[600]),
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _deleteMessage(message);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFEF4444),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  'حذف',
                  style: GoogleFonts.cairo(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
    );
  }
}

import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/painting.dart';
import '../utils/logger.dart';

/// مدير الذاكرة المحسن للتطبيق
class MemoryManager {
  static Timer? _memoryCheckTimer;
  static final Map<String, dynamic> _cache = {};
  static const int _maxCacheSize = 100; // حد أقصى للعناصر المخزنة
  static const Duration _cacheCleanupInterval = Duration(minutes: 5);
  static const Duration _memoryCheckInterval = Duration(minutes: 2);

  /// بدء مدير الذاكرة
  static void startMemoryManager() {
    try {
      _scheduleMemoryChecks();
      _scheduleCacheCleanup();

      AppLogger.info('تم بدء مدير الذاكرة', 'MemoryManager');
    } catch (e) {
      AppLogger.error('خطأ في بدء مدير الذاكرة', 'MemoryManager', e);
    }
  }

  /// إيقاف مدير الذاكرة
  static void stopMemoryManager() {
    _memoryCheckTimer?.cancel();
    _memoryCheckTimer = null;
    _cache.clear();

    AppLogger.info('تم إيقاف مدير الذاكرة', 'MemoryManager');
  }

  /// جدولة فحص الذاكرة الدوري
  static void _scheduleMemoryChecks() {
    _memoryCheckTimer = Timer.periodic(_memoryCheckInterval, (timer) {
      _performMemoryCheck();
    });
  }

  /// جدولة تنظيف الكاش
  static void _scheduleCacheCleanup() {
    Timer.periodic(_cacheCleanupInterval, (timer) {
      _cleanupCache();
    });
  }

  /// فحص استخدام الذاكرة
  static void _performMemoryCheck() {
    try {
      if (kDebugMode && !kIsWeb) {
        // في وضع التطوير وليس على الويب، نعرض معلومات الذاكرة
        try {
          final info = ProcessInfo.currentRss;
          AppLogger.info(
            'استخدام الذاكرة: ${_formatBytes(info)}',
            'MemoryManager',
          );

          // إذا تجاوز الاستخدام حد معين، نقوم بالتنظيف
          if (info > 150 * 1024 * 1024) {
            // 150 MB
            _performEmergencyCleanup();
          }
        } catch (e) {
          // ProcessInfo غير مدعوم على هذه المنصة
          AppLogger.warning(
            'مراقبة الذاكرة غير مدعومة على هذه المنصة',
            'MemoryManager',
          );
        }
      } else if (kIsWeb) {
        // على الويب، نقوم بتنظيف دوري بسيط
        _performBasicCleanup();
      }
    } catch (e) {
      AppLogger.error('خطأ في فحص الذاكرة', 'MemoryManager', e);
    }
  }

  /// تنظيف أساسي للذاكرة (للويب)
  static void _performBasicCleanup() {
    try {
      // تنظيف جزئي للكاش
      if (_cache.length > _maxCacheSize ~/ 2) {
        final keysToRemove = _cache.keys.take(_cache.length ~/ 4).toList();
        for (final key in keysToRemove) {
          _cache.remove(key);
        }
        AppLogger.info(
          'تم التنظيف الأساسي - حذف ${keysToRemove.length} عنصر',
          'MemoryManager',
        );
      }
    } catch (e) {
      AppLogger.error('خطأ في التنظيف الأساسي', 'MemoryManager', e);
    }
  }

  /// تنظيف طارئ للذاكرة
  static void _performEmergencyCleanup() {
    try {
      AppLogger.warning('بدء التنظيف الطارئ للذاكرة', 'MemoryManager');

      // تنظيف الكاش بالكامل
      _cache.clear();

      // تشغيل garbage collector
      if (kDebugMode) {
        // في وضع التطوير فقط
        // System.gc() في Dart غير متاح مباشرة
      }

      AppLogger.info('تم التنظيف الطارئ للذاكرة', 'MemoryManager');
    } catch (e) {
      AppLogger.error('خطأ في التنظيف الطارئ', 'MemoryManager', e);
    }
  }

  /// تنظيف الكاش
  static void _cleanupCache() {
    try {
      if (_cache.length > _maxCacheSize) {
        // حذف النصف الأول من الكاش (الأقدم)
        final keysToRemove = _cache.keys.take(_cache.length ~/ 2).toList();
        for (final key in keysToRemove) {
          _cache.remove(key);
        }

        AppLogger.info(
          'تم تنظيف الكاش - حذف ${keysToRemove.length} عنصر',
          'MemoryManager',
        );
      }
    } catch (e) {
      AppLogger.error('خطأ في تنظيف الكاش', 'MemoryManager', e);
    }
  }

  /// إضافة عنصر للكاش
  static void cacheItem(String key, dynamic value) {
    try {
      if (_cache.length >= _maxCacheSize) {
        // حذف أقدم عنصر
        final firstKey = _cache.keys.first;
        _cache.remove(firstKey);
      }

      _cache[key] = {
        'value': value,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
    } catch (e) {
      AppLogger.error('خطأ في إضافة عنصر للكاش', 'MemoryManager', e);
    }
  }

  /// الحصول على عنصر من الكاش
  static T? getCachedItem<T>(String key) {
    try {
      final item = _cache[key];
      if (item != null) {
        return item['value'] as T?;
      }
      return null;
    } catch (e) {
      AppLogger.error('خطأ في الحصول على عنصر من الكاش', 'MemoryManager', e);
      return null;
    }
  }

  /// حذف عنصر من الكاش
  static void removeCachedItem(String key) {
    try {
      _cache.remove(key);
    } catch (e) {
      AppLogger.error('خطأ في حذف عنصر من الكاش', 'MemoryManager', e);
    }
  }

  /// تنظيف الكاش بالكامل
  static void clearCache() {
    try {
      _cache.clear();
      AppLogger.info('تم تنظيف الكاش بالكامل', 'MemoryManager');
    } catch (e) {
      AppLogger.error('خطأ في تنظيف الكاش', 'MemoryManager', e);
    }
  }

  /// الحصول على إحصائيات الذاكرة
  static Map<String, dynamic> getMemoryStats() {
    try {
      int rssMemory = 0;
      String formattedRssMemory = 'غير متاح';

      if (kDebugMode && !kIsWeb) {
        try {
          rssMemory = ProcessInfo.currentRss;
          formattedRssMemory = _formatBytes(rssMemory);
        } catch (e) {
          // ProcessInfo غير مدعوم على هذه المنصة
          formattedRssMemory = 'غير مدعوم على هذه المنصة';
        }
      }

      return {
        'cacheSize': _cache.length,
        'maxCacheSize': _maxCacheSize,
        'isManagerRunning': _memoryCheckTimer?.isActive ?? false,
        'rssMemory': rssMemory,
        'formattedRssMemory': formattedRssMemory,
        'platform': kIsWeb ? 'Web' : Platform.operatingSystem,
      };
    } catch (e) {
      AppLogger.error('خطأ في جلب إحصائيات الذاكرة', 'MemoryManager', e);
      return {};
    }
  }

  /// تنسيق حجم البايتات
  static String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// تحسين الذاكرة للصور
  static void optimizeImageMemory() {
    try {
      // تنظيف كاش الصور
      PaintingBinding.instance.imageCache.clear();
      PaintingBinding.instance.imageCache.clearLiveImages();

      AppLogger.info('تم تحسين ذاكرة الصور', 'MemoryManager');
    } catch (e) {
      AppLogger.error('خطأ في تحسين ذاكرة الصور', 'MemoryManager', e);
    }
  }

  /// تحسين شامل للذاكرة
  static void performFullOptimization() {
    try {
      AppLogger.info('بدء التحسين الشامل للذاكرة', 'MemoryManager');

      // تنظيف الكاش
      clearCache();

      // تحسين ذاكرة الصور
      optimizeImageMemory();

      // تنظيف طارئ
      _performEmergencyCleanup();

      AppLogger.success('تم التحسين الشامل للذاكرة', 'MemoryManager');
    } catch (e) {
      AppLogger.error('خطأ في التحسين الشامل', 'MemoryManager', e);
    }
  }
}
